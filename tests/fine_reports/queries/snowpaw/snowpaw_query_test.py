"""Tests of the SnowPAW SQL queries.

The queries use Jinja templating to generate SQL statements to run against our Snowflake PAW.

This test module does the following:
- Locates all SnowPAW SQL query files in the repository
- For each query file:
    - Loads the query file
    - Compiles the query file
    - Checks that the compiled query is valid SQL
"""

# mypy: ignore-errors

from pathlib import Path
from typing import cast

import pytest
from sqlalchemy import Compiled, Engine, TextClause, text
from sqlglot import Expression, parse_one
from sqlglot.expressions import Delete, Insert, Join, Schema, Select, Table
from sqlglot.optimizer.scope import Scope, build_scope

from config import settings
from fine_reports.datastore.snowpaw import prepare_query_from_file
from fine_reports.ports.logging import FineReportsLogger, get_logger

logger: FineReportsLogger = get_logger(__name__)

ALLOWED_INSERT_QUERIES: set[tuple[str, str]] = {
    ("client_disbursement", "query_to_cache_remittance_disbursement"),
    ("financial_remittance", "query_to_cache_remittance_disbursement"),
}

ALLOWED_DELETE_QUERIES: set[tuple[str, str]] = {
    ("financial_remittance", "delete_disbursement_for_report"),
}


def generate_raw_sql_query(engine: Engine, query_sql: str, query_params: dict) -> str:
    """Generate a raw SQL query from a Jinja template and parameters.

    Args:
        engine (Engine): The SQLAlchemy engine.
        query_sql (str): The raw SQL query template.
        query_params (dict): Query parameters.

    Returns:
        str: The raw SQL query.
    """
    query: TextClause = text(query_sql)
    compiled: Compiled = query.bindparams(**query_params).compile(
        dialect=engine.dialect,
        compile_kwargs={"literal_binds": True},
    )
    return str(compiled)


def test_that_snowpaw_sql_queries_have_expected_metadata_in_initial_comment(snowpaw_sql_query_file: Path) -> None:
    """Test that the first six lines of each Snowflake SQL query file is a comment with identifying information.

    Args:
        snowpaw_sql_query_file (Path): Path to the SnowPAW SQL query file.
    """
    query_name: str = snowpaw_sql_query_file.name.replace(".jinja.sql", "")
    with open(snowpaw_sql_query_file, "r") as query_file:
        sql_lines: list[str] = query_file.readlines()
        assert len(sql_lines) >= 7, (
            "A query should be at lest 7 lines long - 6 for the required comment and 1+ for the query proper."
        )
        assert sql_lines[0].rstrip("\n") == "/*", "The first line of the query file should be a comment start token."
        assert sql_lines[1].rstrip("\n") == "project: remittance_reports", (
            "The second line of the query file should identify the project."
        )
        assert sql_lines[2].rstrip("\n") == f"query_name: {query_name}", (
            "The third line of the query file should identify the query name."
        )
        assert sql_lines[3].rstrip("\n") == "base_name: {{ base_name | sqlsafe }}", (
            "The fourth line of the query file should identify the base nam."
        )
        assert sql_lines[4].rstrip("\n") == "report_name: {{ report_name | sqlsafe }}", (
            "The fifth line of the query file should identify the report name."
        )
        assert sql_lines[5].rstrip("\n") == "*/", "The sixth line of the query file should be a comment start token."


def test_all_snowpaw_sql_queries_can_be_parsed_with_params(
    base_name: str,
    snowpaw_sql_query_file: Path,
    snowpaw_sql_query_parameters: dict,
    mock_snowflake_engine: Engine,
) -> None:
    """Test that all SnowPAW SQL queries can be parsed with parameter values.

    Args:
        base_name (str): The base name of the query.
        snowpaw_sql_query_file (Path): Path to the SnowPAW SQL query file.
        snowpaw_sql_query_parameters (dict): Query parameters.
        mock_snowflake_engine (Engine): Mock SQLAlchemy engine
    """
    assert snowpaw_sql_query_parameters is not None, (
        f"All SnowPAW queries used by base_name {base_name} must define at least one set of test parameters in "
        f"tests/fixtures/queries/snowpaw/parameters/{base_name}.yaml"
    )
    logger.info(f"Testing SnowPAW SQL query file: {base_name}/{snowpaw_sql_query_file}")
    logger.info(f"Testing SnowPAW SQL parameters: {snowpaw_sql_query_parameters}")

    # add the query parameters that the real Snowflake engine would add
    snowpaw_sql_query_parameters.update(
        {
            "database_name": settings.snowflake_database,
            "base_name": base_name,
        }
    )

    query_str, query_bind_params = prepare_query_from_file(snowpaw_sql_query_file, **snowpaw_sql_query_parameters)

    raw_query_str = generate_raw_sql_query(mock_snowflake_engine, query_str, query_bind_params)

    logger.info(f"Compiled query: {raw_query_str}")

    # Check that the compiled query is valid SQL
    parsed_query: Expression = parse_one(raw_query_str, dialect="snowflake")
    assert parsed_query is not None, "The compiled query should be valid SQL."

    query_name: str = snowpaw_sql_query_file.stem.replace(".jinja", "")  # Remove the `.jinja` extension

    # There are two supported query types. The most common is a SELECT query, which may be used by any type of report.
    # The other is an INSERT query, which is much more limited in scope/usage.
    if isinstance(parsed_query, Select):
        select_query: Select = cast(Select, parsed_query)
        joins = list(select_query.find_all(Join))
        assert len(joins) == 0, "The compiled query should not contain any JOIN clauses."

        query_ast: Scope = build_scope(select_query)
        tables: list[Table] = [
            source
            for scope in query_ast.traverse()
            for _, (__, source) in scope.selected_sources.items()
            if isinstance(source, Table)
        ]

        assert len(tables) == 1, (
            "The compiled query should reference exactly one table, or be an allowed zero-table query."
        )
        table: Table = tables[0]
        logger.info(f"tables: {tables}")

        # Note that our "database"."schema"."table" is "catalog"."db"."this" in sqlgot's AST
        assert table.catalog == "test_crucible", (
            'The compiled query should reference the database from "settings.snowflake_database".'
        )
        assert table.db == "remittance", (
            "The compiled query should reference the 'remittance' schema. Only data in the 'remittance' schema is "
            "accessible to the remittance_reports project."
        )
        assert table.name is not None, "The compiled query should reference a table with a nonempty name."
    elif isinstance(parsed_query, Insert):
        assert (base_name, query_name) in ALLOWED_INSERT_QUERIES, (
            f"The query '{query_name}' under base_name '{base_name}' is not a supported INSERT query."
        )
        insert_query: Insert = cast(Insert, parsed_query)
        insert_target: Schema = insert_query.this
        insert_target_table: Table = insert_target.this

        # Note that our "database"."schema"."table" is "catalog"."db"."this" in sqlgot's AST
        assert insert_target_table.catalog == "test_crucible", (
            'The compiled query should reference the database from "settings.snowflake_database".'
        )
        assert insert_target_table.db == "remittance", (
            "The compiled query should reference the 'remittance' schema. Only data in the 'remittance' schema is "
            "accessible to the remittance_reports project."
        )
        assert insert_target_table.name == "dw_remittance_disbursement", (
            "The compiled INSERT query should target the 'dw_remittance_disbursement' table. "
            "This is the only table that can be used by remittance reports for INSERT queries."
        )
    elif isinstance(parsed_query, Delete):
        assert (base_name, query_name) in ALLOWED_DELETE_QUERIES, (
            f"The query '{query_name}' under base_name '{base_name}' is not a supported DELETE query."
        )
        delete_query: Delete = cast(Delete, parsed_query)
        delete_target_table: Table = delete_query.this

        # Note that our "database"."schema"."table" is "catalog"."db"."this" in sqlgot's AST
        assert delete_target_table.catalog == "test_crucible", (
            'The compiled query should reference the database from "settings.snowflake_database".'
        )
        assert delete_target_table.db == "remittance", (
            "The compiled query should reference the 'remittance' schema. Only data in the 'remittance' schema is "
            "accessible to the remittance_reports project."
        )
        assert delete_target_table.name == "dw_remittance_disbursement", (
            "The compiled DELETE query should target the 'dw_remittance_disbursement' table. "
            "This is the only table that can be used by remittance reports for DELETE queries."
        )
    else:
        pytest.fail(f"Query {base_name}/{query_name} is not a supported query type.")
