"""Tests for the DBT job dependency check functionality in assets.py."""

from datetime import datetime, time, timezone, tzinfo
from typing import Any
from unittest.mock import MagicMock, patch
import zoneinfo

import dagster as dg
from freezegun import freeze_time
from payit_data_common.api.models.enums import Audience, BaseName, Frequency, Purpose
from payit_data_common.api.models.scheduled_job import Arguments, BooleanString, ScheduledJob, ScheduleTimeZone
import pytest

from fine_reports.dagster.legacy.assets import check_dbt_job_dependency


@pytest.fixture(autouse=True)
def mock_kubernetes_environment(monkeypatch: pytest.MonkeyPatch) -> None:
    """Mock the Kubernetes environment check to return True by default.

    This fixture runs automatically for all tests in this file.

    Args:
        monkeypatch: The pytest monkeypatch fixture
    """
    # Mock is_running_in_kubernetes to return True by default
    monkeypatch.setattr("fine_reports.utilities.kubernetes.is_running_in_kubernetes", lambda: True)


class MockDagsterRun:
    """Mock DagsterRun for testing."""

    def __init__(self, run_id: str, job_name: str, start_time: float | None = None):
        """Initialize a mock DagsterRun.

        Args:
            run_id (str): The run ID
            job_name (str): The job name
            start_time (float | None): The start time as epoch seconds, or None
        """
        self.run_id = run_id
        self.job_name = job_name
        self.start_time = start_time


class MockRunRecord:
    """Mock RunRecord for testing."""

    def __init__(self, dagster_run: MockDagsterRun):
        """Initialize a mock RunRecord.

        Args:
            dagster_run (MockDagsterRun): The DagsterRun associated with this record
        """
        self.dagster_run = dagster_run


class MockEventSpecificData:
    """Mock EventSpecificData for testing."""

    def __init__(self, has_materialization: bool = True):
        """Initialize a mock EventSpecificData.

        Args:
            has_materialization (bool): Whether this data has a materialization attribute
        """
        self.materialization: dict[str, Any] | None
        if has_materialization:
            self.materialization = {}
        else:
            self.materialization = None


class MockDagsterEvent:
    """Mock DagsterEvent for testing."""

    def __init__(self, has_materialization: bool = True):
        """Initialize a mock DagsterEvent.

        Args:
            has_materialization (bool): Whether this event has materialization data
        """
        self.event_specific_data = MockEventSpecificData(has_materialization)


class MockEventLogEntry:
    """Mock EventLogEntry for testing."""

    def __init__(self, dagster_event: MockDagsterEvent | None = None):
        """Initialize a mock EventLogEntry.

        Args:
            dagster_event (MockDagsterEvent | None): The mock DagsterEvent associated with this entry
        """
        # Our mock accepts our own MockDagsterEvent class rather than the real DagsterEvent
        self.dagster_event = dagster_event


class MockEventLogRecord:
    """Mock EventLogRecord for testing."""

    def __init__(self, has_event: bool = True, has_materialization: bool = True):
        """Initialize a mock EventLogRecord.

        Args:
            has_event (bool): Whether this record has a DagsterEvent
            has_materialization (bool): Whether the event has materialization data
        """
        if has_event:
            dagster_event = MockDagsterEvent(has_materialization)
        else:
            dagster_event = None

        self.event_log_entry = MockEventLogEntry(dagster_event)


@pytest.fixture
def mock_context() -> MagicMock:
    """Create a mock AssetExecutionContext.

    Returns:
        MagicMock: The mock context
    """
    context = MagicMock(spec=dg.AssetExecutionContext)
    context.log = MagicMock()
    context.instance = MagicMock(spec=dg.DagsterInstance)
    return context


@pytest.fixture
def mock_scheduled_job() -> ScheduledJob:
    """Create a mock ScheduledJob.

    Returns:
        ScheduledJob: The mock job configuration
    """
    arguments_dict: dict[str, str] = {
        "use-snowflake": f"{BooleanString.TRUE.value}",
        "report-date": "yesterday",
        "enable-delivery": f"{BooleanString.FALSE.value}",
        "save-remittance-data": f"{BooleanString.FALSE.value}",
    }
    scheduled_job_dict: dict[str, Any] = {
        "name": "prev_test_job",
        "baseName": BaseName.DATABASE_CONNECTION_TEST,
        "audience": Audience.INTERNAL,
        "frequency": Frequency.DAILY,
        "purpose": Purpose.PREVIEW,
        "scheduleTimeZone": ScheduleTimeZone.AMERICA_CHICAGO,
        "arguments": Arguments(**arguments_dict),  # type: ignore[arg-type]
        "created": True,
        "enabled": True,
    }
    return ScheduledJob(**scheduled_job_dict)


@pytest.fixture
def midnight_timestamp() -> float:
    """Get the timestamp for midnight in Central time.

    Returns:
        float: The timestamp for midnight in UTC
    """
    central_tz: tzinfo = zoneinfo.ZoneInfo("America/Chicago")
    now_central: datetime = datetime.now(central_tz)
    # Calculate midnight for the current day in Central time
    midnight_central: datetime = datetime.combine(now_central.date(), time(0, 0), central_tz)
    # Convert to UTC timestamp
    return midnight_central.astimezone(timezone.utc).timestamp()


def test_skip_check_for_non_preview_job() -> None:
    """Test that the check is skipped for non-preview jobs."""
    # Create mock context
    context = MagicMock()
    context.log = MagicMock()
    context.instance = MagicMock()

    # Call the function with a non-preview job
    check_dbt_job_dependency(
        context=context,
        is_preview_job=False,
        job_name="test_job",
        schedule_time_zone_value="America/Chicago",
        uses_snowflake=True,
    )

    # Verify that no logs were created and get_run_records was not called
    context.log.info.assert_not_called()
    context.instance.get_run_records.assert_not_called()


def test_skip_check_for_non_snowflake_job() -> None:
    """Test that the check is skipped for jobs that don't use Snowflake."""
    # Create mock context
    context = MagicMock()
    context.log = MagicMock()
    context.instance = MagicMock()

    # Call the function with a job that doesn't use Snowflake
    check_dbt_job_dependency(
        context=context,
        is_preview_job=True,
        job_name="test_job",
        schedule_time_zone_value="America/Chicago",
        uses_snowflake=False,
    )

    # Verify that no logs were created and get_run_records was not called
    context.log.info.assert_not_called()
    context.instance.get_run_records.assert_not_called()


def test_skip_check_when_not_in_kubernetes() -> None:
    """Test that the check is skipped when not running in Kubernetes."""
    # Create mock context
    context = MagicMock()
    context.log = MagicMock()
    context.instance = MagicMock()

    # Patch is_running_in_kubernetes to return False for this specific test
    with patch("fine_reports.utilities.kubernetes.is_running_in_kubernetes", return_value=False):
        # Call the function with a preview job that uses Snowflake
        check_dbt_job_dependency(
            context=context,
            is_preview_job=True,
            job_name="test_job",
            schedule_time_zone_value="America/Chicago",
            uses_snowflake=True,
        )

    # Verify that the log was created with the expected message
    context.log.info.assert_called_once()
    assert "Skipping DBT dependency check" in context.log.info.call_args[0][0]


@patch("fine_reports.utilities.kubernetes.is_running_in_kubernetes", return_value=True)
@patch("fine_reports.utilities.kubernetes._kubernetes_service_account_token_file_is_present", return_value=True)
@patch("fine_reports.utilities.kubernetes._kubernetes_environment_variables_are_present", return_value=True)
def test_skip_check_when_config_sets_skip_dbt_dependency_checks_to_true(_is_present, _env_present, _is_k8s) -> None:
    """Test that the check is skipped when skip_dbt_dependency_checks is set to True."""
    # Create mock context
    context = MagicMock()
    context.log = MagicMock()
    context.instance = MagicMock()

    # Call the function with a preview job that uses Snowflake and skip_dbt_dependency_checks=True
    check_dbt_job_dependency(
        context=context,
        is_preview_job=True,
        job_name="test_job",
        schedule_time_zone_value="America/Chicago",
        uses_snowflake=True,
        skip_dbt_dependency_checks=True,
    )

    # Verify that the log was created with the expected message
    context.log.info.assert_called_once()
    assert "Skipping DBT dependency check" in context.log.info.call_args[0][0]
    assert "skip_dbt_dependency_checks setting is True" in context.log.info.call_args[0][0]


@patch("fine_reports.utilities.kubernetes.is_running_in_kubernetes", return_value=True)
@patch("fine_reports.utilities.kubernetes._kubernetes_service_account_token_file_is_present", return_value=True)
@patch("fine_reports.utilities.kubernetes._kubernetes_environment_variables_are_present", return_value=True)
def test_check_with_no_runs(_is_present, _env_present, _is_k8s) -> None:
    """Test that an error is raised when no successful DBT runs are found after midnight."""
    # Create a mock context
    context = MagicMock()
    context.log = MagicMock()
    context.instance = MagicMock()

    # Mock get_run_records to return an empty list (no successful runs)
    context.instance.get_run_records.return_value = []

    # Call the function with a preview job that uses Snowflake
    # This should raise a ValueError because no successful runs were found
    with pytest.raises(ValueError) as excinfo:
        check_dbt_job_dependency(
            context=context,
            is_preview_job=True,
            job_name="test_job",
            schedule_time_zone_value="America/Chicago",
            uses_snowflake=True,
        )

    # Verify that the error message contains the expected text
    assert "no successful runs were found" in str(excinfo.value)
    assert "snowpaw_dbt_build_nightly_remittance" in str(excinfo.value)


@patch("fine_reports.utilities.kubernetes.is_running_in_kubernetes", return_value=True)
@patch("fine_reports.utilities.kubernetes._kubernetes_service_account_token_file_is_present", return_value=True)
@patch("fine_reports.utilities.kubernetes._kubernetes_environment_variables_are_present", return_value=True)
def test_successful_check_with_valid_runs(_is_present, _env_present, _is_k8s, midnight_timestamp: float) -> None:
    """Test that the check succeeds when valid runs are found.

    Args:
        _is_present: Mocked _kubernetes_service_account_token_file_is_present
        _env_present: Mocked _kubernetes_environment_variables_are_present
        _is_k8s: Mocked is_running_in_kubernetes
        midnight_timestamp: Fixture that returns midnight in Central time as UTC timestamp
    """
    # Create mock context
    context = MagicMock()
    context.log = MagicMock()
    context.instance = MagicMock()

    # Create a mock run record with a start time after midnight
    mock_run = MockDagsterRun(
        run_id="test-run-id",
        job_name="snowpaw_dbt_build_nightly_remittance",
        start_time=midnight_timestamp + 3600,  # 1 hour after midnight
    )
    mock_record = MockRunRecord(mock_run)

    # Mock get_run_records to return our mock record
    context.instance.get_run_records.return_value = [mock_record]

    # Call the function - it should not raise an error
    check_dbt_job_dependency(
        context=context,
        is_preview_job=True,
        job_name="test_job",
        schedule_time_zone_value="America/Chicago",
        uses_snowflake=True,
    )

    # Verify that the log was created with the expected message
    context.log.info.assert_called()
    assert "Found successful run of" in context.log.info.call_args[0][0]
    assert "test-run-id" in context.log.info.call_args[0][0]


@patch("fine_reports.utilities.kubernetes.is_running_in_kubernetes", return_value=True)
@patch("fine_reports.utilities.kubernetes._kubernetes_service_account_token_file_is_present", return_value=True)
@patch("fine_reports.utilities.kubernetes._kubernetes_environment_variables_are_present", return_value=True)
def test_check_with_only_before_midnight_runs(_is_present, _env_present, _is_k8s, midnight_timestamp: float) -> None:
    """Test that an error is raised when only runs before midnight are found.

    Args:
        _is_present: Mocked _kubernetes_service_account_token_file_is_present
        _env_present: Mocked _kubernetes_environment_variables_are_present
        _is_k8s: Mocked is_running_in_kubernetes
        midnight_timestamp: Fixture that returns midnight in Central time as UTC timestamp
    """
    # Create mock context
    context = MagicMock()
    context.log = MagicMock()
    context.instance = MagicMock()

    # Create a mock run record with a start time before midnight
    mock_run = MockDagsterRun(
        run_id="test-run-id",
        job_name="snowpaw_dbt_build_nightly_remittance",
        start_time=midnight_timestamp - 3600,  # 1 hour before midnight
    )
    mock_record = MockRunRecord(mock_run)

    # Mock get_run_records to return our mock record (which is before midnight)
    context.instance.get_run_records.return_value = [mock_record]

    # Call the function - it should raise a ValueError because the run is before midnight
    with pytest.raises(ValueError) as excinfo:
        check_dbt_job_dependency(
            context=context,
            is_preview_job=True,
            job_name="test_job",
            schedule_time_zone_value="America/Chicago",
            uses_snowflake=True,
        )

    # Verify that the error message contains the expected text
    assert "no successful runs were found" in str(excinfo.value)
    assert "snowpaw_dbt_build_nightly_remittance" in str(excinfo.value)


@patch("fine_reports.dagster.legacy.assets.check_dbt_job_dependency")
def test_asset_integration(mock_check_dbt_job_dependency) -> None:
    """Test the integration with the asset generation function."""
    from payit_data_common.api.models.enums import Audience, BaseName, Frequency, Purpose
    from payit_data_common.api.models.scheduled_job import Arguments, BooleanString, ScheduledJob, ScheduleTimeZone

    from fine_reports.dagster.legacy.assets import generate_remittance_reports_asset

    # Create a mock scheduled job - explicitly set ftp to None to avoid environment variable error
    arguments_dict: dict[str, str | None] = {
        "use-snowflake": f"{BooleanString.TRUE.value}",
        "report-date": "yesterday",
        "enable-delivery": f"{BooleanString.FALSE.value}",
        "save-remittance-data": f"{BooleanString.FALSE.value}",
        "ftp": None,  # Explicitly set to None to avoid FTP env var checks
    }
    scheduled_job_dict: dict[str, Any] = {
        "name": "prev_test_job",
        "baseName": BaseName.DATABASE_CONNECTION_TEST,
        "audience": Audience.INTERNAL,
        "frequency": Frequency.DAILY,
        "purpose": Purpose.PREVIEW,
        "scheduleTimeZone": ScheduleTimeZone.AMERICA_CHICAGO,
        "arguments": Arguments(**arguments_dict),  # type: ignore[arg-type]
        "created": True,
        "enabled": True,
    }
    scheduled_job: ScheduledJob = ScheduledJob(**scheduled_job_dict)

    # Generate an asset for the scheduled job
    with freeze_time("2023-01-01"):
        asset_def, _ = generate_remittance_reports_asset(scheduled_job)

    # Create a mock context that will be used
    context = MagicMock(spec=dg.AssetExecutionContext)
    context.run = MagicMock()
    context.run.job_name = "test_job"
    context.run.run_id = "test_run_id"

    # Set up return value for the mocked function
    mock_check_dbt_job_dependency.return_value = None

    # Call check_dbt_job_dependency as it would be called in the asset compute function
    from fine_reports.dagster.legacy.assets import check_dbt_job_dependency

    check_dbt_job_dependency(
        context=context,
        is_preview_job=True,  # Preview job
        job_name="prev_test_job",
        schedule_time_zone_value="America/Chicago",
        uses_snowflake=True,  # Uses Snowflake
    )

    # Verify the function was called with the expected parameters
    mock_check_dbt_job_dependency.assert_called_once()
    call_args = mock_check_dbt_job_dependency.call_args[1]
    assert call_args["is_preview_job"] is True
    assert call_args["job_name"] == "prev_test_job"
    assert call_args["schedule_time_zone_value"] == "America/Chicago"
    assert call_args["uses_snowflake"] is True
