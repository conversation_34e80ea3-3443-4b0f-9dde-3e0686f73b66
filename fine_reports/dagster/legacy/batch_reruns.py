"""Utilities for defining Dagster Job resources that can materialize many remittance report assets in a single run."""

import warnings

import dagster as dg
from dagster._core.definitions.unresolved_asset_job_definition import UnresolvedAssetJobDefinition
from dagster_k8s import k8s_job_executor
from payit_data_common.api.models.enums import BaseName, Purpose
from payit_data_common.api.models.scheduled_job import Arguments, ScheduledJob

from fine_reports.dagster.legacy.configuration import FineReportsLegacyConfig
from fine_reports.dagster.legacy.generators import get_payit_environment, get_scheduled_job_resources_for_env
from fine_reports.ports.logging import FineReportsLogger, get_logger
from fine_reports.utilities.kubernetes import is_running_in_kubernetes

# The `owners` asset argument is experimental. The warnings about that are noisy, so we ignore them.
warnings.filterwarnings("ignore", category=dg.ExperimentalWarning)

logger: FineReportsLogger = get_logger(__name__)

REPORT_DATES_TO_RERUN: list[str] = [
    "2025-06-09",
]

FINANCIAL_REMITTANCE_ASSETS_TO_RERUN_IN_PROD: list[str] = [
    "aldine_tx_isd_property_tax_financial_remittance_daily_client",
    "aldine_tx_isd_property_tax_pos_financial_remittance_daily_client",
    "anoka_county_misc_pos_financial_remittance_daily_client",
    "anoka_county_property_tax_financial_remittance_daily_client",
    "anoka_county_property_tax_pos_financial_remittance_daily_client",
    "arkansas_outdoors_sdk_financial_remittance_daily_client",
    "arkansas_outdoors_sdk_return_financial_remittance_daily_client",
    "buffalo_ny_non_integrated_pos_financial_remittance_daily_client",
    "buffalo_ny_parking_unlisted_forms_import_daily_client",
    "cabarrus_licensing_pos_financial_remittance_daily_client",
    "cabarrus_solid_waste_pos_financial_remittance_daily_client",
    "champaign_county_courts_pos_financial_remittance_daily_client",
    "co_dl_financial_remittance_daily_client",
    "columbia_mo_utilities_refunds_import_daily_client",
    "columbia_mo_utilities_returns_import_daily_client",
    "cumberland_county_nc_utilities_external_financial_remittance_daily_client",
    "cumberland_county_nc_utilities_nonintegrated_financial_remittance_daily_client",
    "fldmv_financial_remittance_daily_client",
    "gr_property_tax_financial_remittance_daily_client",
    "gr_water_financial_remittance_daily_client_v2",
    "jackson_county_pos_financial_remittance_daily_client",
    "jackson_county_property_tax_pos_financial_remittance_daily_client",
    "johnston_co_nc_property_tax_pos_financial_remittance_daily_client",
    "kdhe_rlp_financial_remittance_daily_client",
    "kdhe_vitals_financial_remittance_daily_client",
    "kent_pos_courts_financial_remittance_daily_client",
    "kent_pos_health_department_financial_remittance_daily_client",
    "kent_pos_prop_tax_financial_remittance_daily_client",
    "kent_pos_public_works_financial_remittance_daily_client",
    "kent_pos_road_commission_financial_remittance_daily_client",
    "kent_probate_courts_pos_financial_remittance_daily_client",
    "knox_county_circuit_court_pos_financial_remittance_daily_client",
    "knox_county_civil_sessions_pos_financial_remittance_daily_client",
    "knox_county_court_pos_financial_remittance_daily_client",
    "lansing_mi_building_safety_pos_financial_remittance_daily_client",
    "lansing_mi_clerks_office_pos_financial_remittance_daily_client",
    "lansing_mi_code_compliance_pos_financial_remittance_daily_client",
    "lansing_mi_fire_dept_pos_financial_remittance_daily_client",
    "lansing_mi_police_dept_pos_financial_remittance_daily_client",
    "lansing_mi_public_service_pos_financial_remittance_daily_client",
    "lansing_mi_treasury_pos_financial_remittance_daily_client",
    "mississippi_outdoors_lifetime_pos_financial_remittance_daily_client",
    "mississippi_outdoors_pos_financial_remittance_daily_client",
    "mississippi_outdoors_sdk_financial_remittance_daily_client",
    "mississippi_outdoors_sdk_return_financial_remittance_daily_client",
    "nc_commercial_vehicle_citation_financial_remittance_daily_client",
    "nc_commercial_vehicle_citation_return_financial_remittance_daily_client",
    "nc_dl_financial_remittance_daily_client",
    "nc_dl_return_financial_remittance_daily_client",
    "nc_ferry_pos_ivr_financial_remittance_daily_client",
    "nc_hurricane_donation_remittance_daily_client",
    "nc_hurricane_donation_telethon_remittance_daily_client",
    "nc_marine_fisheries_pos_financial_remittance_daily_client",
    "ncdmv_civil_penalty_license_and_theft_financial_remittance_daily_client",
    "ncdmv_civil_penalty_license_and_theft_return_financial_remittance_daily_client",
    "ncdmv_crash_reports_financial_remittance_daily_client",
    "ncdmv_driveway_permits_financial_remittance_daily_client",
    "ncdmv_driveway_permits_return_financial_remittance_daily_client",
    "ncdmv_driving_record_financial_remittance_daily_client",
    "ncdmv_driving_record_return_financial_remittance_daily_client",
    "ncdmv_financial_remittance_v2_daily_client",
    "ncdmv_handicap_placard_financial_remittance_daily_client",
    "ncdmv_handicap_placard_return_financial_remittance_daily_client",
    "ncdmv_hearing_fee_financial_remittance_daily_client",
    "ncdmv_hearing_fee_license_and_theft_financial_remittance_daily_client",
    "ncdmv_hearing_fee_license_and_theft_return_financial_remittance_daily_client",
    "ncdmv_hearing_fee_lite_financial_remittance_daily_client",
    "ncdmv_hearing_fee_lite_return_financial_remittance_daily_client",
    "ncdmv_hearing_fee_return_financial_remittance_daily_client",
    "ncdmv_lapse_payment_financial_remittance_daily_client",
    "ncdmv_lapse_payment_return_financial_remittance_daily_client",
    "ncdmv_return_financial_remittance_daily_client",
    "orange_county_pos_financial_remittance_daily_client",
    "pitt_county_nc_animal_svcs_pos_financial_remittance_daily_client",
    "pitt_county_nc_ptax_nonintegrated_pos_financial_remittance_daily_client",
    "pitt_county_nc_public_health_pos_financial_remittance_daily_client",
    "pitt_county_nc_register_deeds_pos_financial_remittance_daily_client",
    "pitt_county_nc_solid_waste_pos_financial_remittance_daily_client",
    "pueblo_co_property_tax_financial_remittance_daily_client",
    "pueblo_co_property_tax_returns_financial_remittance_daily_client",
    "shelby_tax_financial_remittance_daily_client_v2",
    "shelby_tax_financial_remittance_daily_client",
    "southaven_ms_animalshelter_pos_financial_remittance_daily_client",
    "southaven_ms_clerks_office_pos_financial_remittance_daily_client",
    "southaven_ms_policedepartment_pos_financial_remittance_daily_client",
    "southaven_ms_utilities_pos_financial_remittance_daily_client",
    "stl_business_pos_financial_daily_client",
    "stl_earnings_tax_pos_financial_daily_client",
    "stl_excise_pos_financial_remittance_daily_client",
    "stl_forestry_pos_financial_remittance_daily_client",
    "stl_healthservices_pos_financial_remittance_daily_client",
    "stl_parks_pos_financial_remittance_daily_client",
    "stl_police_pos_financial_daily_client",
    "stl_property_financial_remittance_daily_v2_client",
    "stl_property_tax_pos_financial_daily_client",
    "stl_publicservices_pos_financial_remittance_daily_client",
    "stl_real_estate_financial_remittance_daily_v2_client",
    "stl_soulardmarket_pos_financial_remittance_daily_client",
    "stl_streets_pos_financial_remittance_daily_client",
    "stl_water_financial_remittance_v2_daily_client",
    "virginia_licensing_import_remittance_daily_client",
    "virginia_licensing_readable_import_remittance_daily_client",
    "wyandotte_county_health_pos_financial_remittance_daily_client",
    "wyandotte_county_pos_ptax_financial_remittance_daily_client",
]

FINANCIAL_REMITTANCE_ASSETS_TO_RERUN_IN_CA_PROD: list[str] = [
    "essex_on_property_tax_financial_remittance_daily_client",
    "essex_on_utilities_financial_remittance_daily_client",
    "toronto_parking_violations_daily_deposit_record_daily_prod_client",
    "toronto_parking_violations_fees_remittance_daily_prod_client",
    "toronto_parking_violations_financial_remittance_daily_prod_client",
    "toronto_property_tax_fees_remittance_daily_prod_client",
    "toronto_property_tax_financial_remittance_daily_prod_client",
    "toronto_utilities_fees_remittance_daily_prod_client",
    "toronto_utilities_financial_remittance_daily_prod_client",
]

FINANCIAL_REMITTANCE_ASSETS_TO_RERUN_BY_ENVIRONMENT: dict[str, list[str]] = {
    "dev": [],
    "system": [],
    "staging": [],
    "prod": FINANCIAL_REMITTANCE_ASSETS_TO_RERUN_IN_PROD,
    "ca-staging": [],
    "ca-prod": FINANCIAL_REMITTANCE_ASSETS_TO_RERUN_IN_CA_PROD,
}


def is_eligible_for_batch_rerun(scheduled_job: ScheduledJob) -> bool:
    """Determine if a scheduled job is eligible for batch rerun.

    Args:
        scheduled_job (ScheduledJob): The scheduled job to check.

    Returns:
        bool: True if the scheduled job is eligible for batch rerun, False otherwise.
    """
    if scheduled_job.purpose != Purpose.STANDARD:
        return False
    if scheduled_job.base_name != BaseName.FINANCIAL_REMITTANCE:
        return False
    if scheduled_job.created is False:
        return False
    if scheduled_job.enabled is False:
        return False
    if scheduled_job.arguments is None:
        return False
    return True


def define_rerun_job_for_report_date(report_date: str) -> UnresolvedAssetJobDefinition:
    """Get the asset keys for the rerun job for a given report date.

    Args:
        report_date (str): The report date to get the rerun asset keys for.

    Returns:
        UnresolvedAssetJobDefinition: The rerun job for the given report date.
    """
    environment: str = get_payit_environment()
    all_scheduled_jobs_for_environment: list[ScheduledJob] = get_scheduled_job_resources_for_env()
    job_names_for_environment: list[str] = FINANCIAL_REMITTANCE_ASSETS_TO_RERUN_BY_ENVIRONMENT[environment]
    scheduled_jobs_to_rerun: list[ScheduledJob] = [
        sj
        for sj in all_scheduled_jobs_for_environment
        if is_eligible_for_batch_rerun(sj) and sj.name in job_names_for_environment
    ]

    expected_blank_on_2025_06_09: set[str] = {
        "columbia_mo_utilities_refunds_import_daily_client",
        "columbia_mo_utilities_returns_import_daily_client",
        "kdhe_rlp_financial_remittance_daily_client",
        "nc_hurricane_donation_remittance_daily_client",
        "nc_hurricane_donation_telethon_remittance_daily_client",
    }

    if report_date == "2025-06-09":
        scheduled_jobs_to_rerun = [sj for sj in scheduled_jobs_to_rerun if sj.name not in expected_blank_on_2025_06_09]

    op_configs: dict[str, FineReportsLegacyConfig] = {}
    asset_keys: list[dg.AssetKey] = []

    scheduled_job: ScheduledJob
    for scheduled_job in scheduled_jobs_to_rerun:
        run_config_settings: dict[str, str | bool] = {}
        run_config_settings["report_date"] = report_date

        arguments: Arguments | None = scheduled_job.arguments
        if environment == "prod":
            if arguments is not None and arguments.enable_delivery is not None:
                run_config_settings["enable_delivery"] = arguments.enable_delivery.value == "true"
            if arguments is not None and arguments.save_remittance_data is not None:
                run_config_settings["save_remittance_data"] = arguments.save_remittance_data.value == "true"
        else:
            run_config_settings["enable_delivery"] = False
            run_config_settings["save_remittance_data"] = False

        ## both of these were validated in the is_eligible_for_batch_rerun function but we handle None
        ## explicitly here for type checking - mypy will complain otherwise
        base_name_value: str = f"{scheduled_job.base_name.value}" if scheduled_job.base_name else ""
        scheduled_job_name: str = f"{scheduled_job.name}" if scheduled_job.name else ""
        op_name: str = f"{base_name_value}__{scheduled_job_name}"
        op_configs[op_name] = FineReportsLegacyConfig(**run_config_settings)  # type: ignore[arg-type]
        asset_key: dg.AssetKey = dg.AssetKey([base_name_value, scheduled_job_name])
        asset_keys.append(asset_key)

    logger.info(f"Asset keys: {asset_keys}")

    sanitized_report_date: str = report_date.replace("-", "_")

    if is_running_in_kubernetes():
        executor_def: dg.ExecutorDefinition = k8s_job_executor.configured({"max_concurrent": 30})
    else:
        executor_def = dg.multiprocess_executor.configured({"max_concurrent": 1})

    logger.info(f"rerun job op_config keys: {op_configs.keys()}")

    asset_job_definition: UnresolvedAssetJobDefinition = dg.define_asset_job(
        name=f"ei2_856_rerun_reports_for_report_date_{sanitized_report_date}",
        selection=dg.AssetSelection.keys(*asset_keys),
        description=(f"Rerun reports impacted by EI2-856 for report_date {report_date}."),
        tags={
            "com.payit/incident": "EI2-856",
            "com.payit/batch-rerun": "true",
            "com.payit/report-date": report_date,
            "dagster-k8s/config": {
                "pod_spec_config": {
                    # Set a long grace period to terminate many pending or running step pods.
                    "terminationGracePeriodSeconds": (5 * 60),
                },
            },
        },
        config=dg.RunConfig(ops=op_configs),
        executor_def=executor_def,
    )

    return asset_job_definition
