"""Dagster assets module."""

from datetime import datetime, time, timezone, tzinfo
import json
from typing import Any, Sequence
import warnings
import zoneinfo

import dagster as dg
from dagster._core.events import EventSpecificData
from payit_data_common.api.models.r1dot5.report_configuration import ReportDataSources, SnowpawRemittanceDataSource
from payit_data_common.api.models.report_configuration import ReportConfiguration
from payit_data_common.api.models.scheduled_job import Arguments, BooleanString, ScheduledJob

from fine_reports.configuration.io import get_report_configuration
from fine_reports.dagster.constants import DATA_TEAM_OWNER
from fine_reports.dagster.legacy.configuration import FineReportsLegacyConfig
from fine_reports.ports.logging import FineReportsLogger, get_logger
from fine_reports.utilities.kubernetes import is_running_in_kubernetes

# The `owners` asset argument is experimental. The warnings about that are noisy, so we ignore them.
warnings.filterwarnings("ignore", category=dg.ExperimentalWarning)

logger: FineReportsLogger = get_logger(__name__)


def set_config_value(
    tags: dict[str, str],
    settings_update_data: dict[str, Any],
    key: str,
    value: str | BooleanString | None,
    tag_it: bool = False,
) -> None:
    """Set the value in the tags and settings_update_data dictionaries.

    Args:
        tags (dict[str, str]): The tags dictionary to update.
        settings_update_data (dict[str, Any]): The settings_update_data dictionary to update.
        key (str): The key to use for the tag and settings_update_data dictionaries.
        value (str | BooleanString | None): The value to use, if not None.
        tag_it (bool): Whether to include this setting in Dagster asset tags, default False.

    Returns:
        None - the tags and settings_update_data dictionaries are updated in place.
    """
    value_string: str | None = f"{value.value}" if isinstance(value, BooleanString) else value

    if value_string is None:
        return

    settings_update_data[key] = value_string

    if not tag_it:
        return

    tag_key: str = f"com.payit.fine-reports/{key.replace('_', '-')}"
    tag_sanitized_key: str = f"com.payit.fine-reports.{key.replace('_', '-')}/sanitized"
    tag_truncated_key: str = f"com.payit.fine-reports.{key.replace('_', '-')}/truncated"
    tag_value: str | None = value_string
    ## If a tag value contains characters that need to be escaped, replace them with allowed characters
    ## and add a tag to indicate that the value has been sanitized
    if ":" in value_string:
        tag_value = value_string.replace(":", ".")
        tags[tag_sanitized_key] = "true"
    ## If a tag value is overlong (max length is 63 characters), truncate it to 63 characters
    ## and add a tag to indicate that the value has been truncated
    if len(value_string) > 63:
        tag_value = value_string[:63]
        tags[tag_truncated_key] = "true"
    ## NOTE the above two scenarios aren't combinative - only one will apply. (Do we need to handle both scenarios?)

    ## Only add a k/v pair to the tags dictionary if the value is not None
    if tag_value:
        tags[tag_key] = tag_value


def check_dbt_job_dependency(
    context: dg.AssetExecutionContext,
    is_preview_job: bool,
    job_name: str,
    schedule_time_zone_value: str,
    uses_snowflake: bool,
    skip_dbt_dependency_checks: bool = False,
    dbt_asset_dependencies: list[list[str]] | None = None,
) -> None:
    """Check if the specific DBT model dependencies have been successfully materialized.

    Args:
        context (dg.AssetExecutionContext): The Dagster asset execution context
        is_preview_job (bool): Whether this is a preview job
        job_name (str): Name of the current job being executed
        schedule_time_zone_value (str): The value of the schedule_time_zone field
        uses_snowflake (bool): Whether the job uses Snowflake
        skip_dbt_dependency_checks (bool): Whether to skip the DBT dependency check
        dbt_asset_dependencies (list[list[str]]): List of asset keys for the specific DBT models

    Raises:
        ValueError: If this is a preview job that uses Snowflake and any of its DBT model dependencies
            have not been successfully materialized
    """
    # Skip the check if not a preview job or if the job doesn't use Snowflake
    if not is_preview_job or not uses_snowflake:
        return

    # Skip the check if not running in Kubernetes (e.g., local development)
    if not is_running_in_kubernetes():
        context.log.info(
            f"Skipping DBT dependency check for job '{job_name}' since we're not running in Kubernetes. "
            "This check is bypassed in local development environments."
        )
        return

    if skip_dbt_dependency_checks:
        context.log.info(
            f"Skipping DBT dependency check for job '{job_name}' since the skip_dbt_dependency_checks setting is True."
        )
        return

    # If no specific dependencies are provided, fall back to checking the entire DBT job
    if not dbt_asset_dependencies:
        check_entire_dbt_job(context, job_name, schedule_time_zone_value)
    else:
        # Check each specific DBT model dependency
        check_specific_dbt_dependencies(context, job_name, dbt_asset_dependencies)


def check_entire_dbt_job(context: dg.AssetExecutionContext, job_name: str, schedule_time_zone_value: str) -> None:
    """Check if the entire DBT job has run successfully since midnight.

    Args:
        context (dg.AssetExecutionContext): The Dagster asset execution context
        job_name (str): Name of the current job being executed
        schedule_time_zone_value (str): The value of the schedule_time_zone field

    Raises:
        ValueError: If the dependent DBT job has not run successfully since midnight
    """
    # Only check dependencies for preview jobs that use Snowflake and are running in Kubernetes
    central_tz: tzinfo = zoneinfo.ZoneInfo(schedule_time_zone_value)
    now_central: datetime = datetime.now(central_tz)

    # Calculate midnight for the current day in Central time
    midnight_central: datetime = datetime.combine(now_central.date(), time(0, 0), central_tz)

    # Convert to UTC timestamp for Dagster query
    midnight_utc_timestamp: float = midnight_central.astimezone(timezone.utc).timestamp()

    # Set up filter to query for successful runs of the DBT job
    dbt_job_name: str = "snowpaw_dbt_build_nightly_remittance"
    dbt_code_location: str = "snowpaw-dbt"

    # Get the Dagster instance from the context
    dagster_instance: dg.DagsterInstance = context.instance

    # Query for successful runs of the DBT job after midnight
    run_filter: dg.RunsFilter = dg.RunsFilter(
        job_name=dbt_job_name,
        statuses=[dg.DagsterRunStatus.SUCCESS],
        tags={"dagster/code_location": dbt_code_location},
    )

    # Get run records - ensure we only consider runs after midnight
    run_records: Sequence[dg.RunRecord] = dagster_instance.get_run_records(
        filters=run_filter,
        limit=5,  # Get a few to find the most recent one after midnight
    )

    # Filter runs to find those after midnight
    valid_runs = []
    for record in run_records:
        # Each RunRecord has a dagster_run property
        dagster_run = record.dagster_run

        # Check if this run started after midnight
        run_start_time = getattr(dagster_run, "start_time", None)
        if run_start_time is not None and run_start_time >= midnight_utc_timestamp:
            valid_runs.append(record)

    # If no successful runs are found after midnight, raise an error
    if not valid_runs:
        error_message: str = (
            f"Preview job '{job_name}' depends on successful completion of "
            f"'{dbt_job_name}' in code location '{dbt_code_location}', "
            f"but no successful runs were found after {midnight_central.isoformat()}. "
            f"Please check if the nightly DBT job has run successfully."
        )
        context.log.error(error_message)
        raise ValueError(error_message)

    # Get the DagsterRun from the first valid RunRecord
    successful_run = valid_runs[0].dagster_run

    # Format the timestamp for logging
    start_time_str = "unknown"
    run_start_time = getattr(successful_run, "start_time", None)
    if run_start_time is not None:
        start_time = datetime.fromtimestamp(run_start_time, timezone.utc)
        start_time_str = start_time.isoformat()

    # Get run ID safely
    run_id = getattr(successful_run, "run_id", "[unknown run ID]")

    context.log.info(f"Found successful run of '{dbt_job_name}' with run ID {run_id} started at {start_time_str} UTC")


def check_specific_dbt_dependencies(
    context: dg.AssetExecutionContext, job_name: str, dbt_asset_dependencies: list[list[str]]
) -> None:
    """Check if each specific DBT model dependency has been successfully materialized.

    Args:
        context (dg.AssetExecutionContext): The Dagster asset execution context
        job_name (str): Name of the current job being executed
        dbt_asset_dependencies (list[list[str]]): List of asset keys for the specific DBT models

    Raises:
        ValueError: If any of the specific DBT model dependencies have not been successfully materialized
    """
    # Get the Dagster instance from the context
    dagster_instance: dg.DagsterInstance = context.instance

    # Track any failed dependencies
    failed_dependencies = []

    for asset_key in dbt_asset_dependencies:
        # Convert the asset key list to a proper AssetKey object
        asset_key_obj = dg.AssetKey(asset_key)

        # Get the most recent materialization event for this asset
        events: Sequence[dg.EventLogRecord] = dagster_instance.get_event_records(
            dg.EventRecordsFilter(
                event_type=dg.DagsterEventType.ASSET_MATERIALIZATION,
                asset_key=asset_key_obj,
            ),
            limit=1,
        )

        if not events:
            # No materialization events found for this asset
            failed_dependencies.append("/".join(asset_key))
            context.log.warning(f"No materialization events found for asset {asset_key_obj}")
            continue

        # Check if the most recent run was successful
        latest_event = events[0]
        dagster_event: dg.DagsterEvent | None = latest_event.event_log_entry.dagster_event
        if dagster_event is None:
            # This shouldn't happen, but if it does, consider it a failure
            failed_dependencies.append("/".join(asset_key))
            context.log.warning(f"Invalid materialization event found for asset {asset_key_obj}")
            continue

        event_data: EventSpecificData | None = dagster_event.event_specific_data

        if event_data is None:
            # This shouldn't happen, but if it does, consider it a failure
            failed_dependencies.append("/".join(asset_key))
            context.log.warning(f"Invalid materialization event found for asset {asset_key_obj}")
            continue

        if getattr(event_data, "materialization", None) is None:
            # This shouldn't happen, but if it does, consider it a failure
            failed_dependencies.append("/".join(asset_key))
            context.log.warning(f"Invalid materialization event found for asset {asset_key_obj}")
            continue

        # At this point, we've found a valid materialization event
        context.log.info(f"Found successful materialization for asset {asset_key_obj}")

    # If any dependencies failed, raise an error
    if failed_dependencies:
        error_message: str = (
            f"Preview job '{job_name}' depends on these DBT models, but they haven't been successfully "
            f"materialized: {', '.join(failed_dependencies)}. "
            f"Please check if these models have been built successfully."
        )
        context.log.error(error_message)
        raise ValueError(error_message)

    context.log.info(f"All {len(dbt_asset_dependencies)} DBT model dependencies have been successfully materialized.")


def generate_remittance_reports_asset(
    scheduled_job: ScheduledJob,
) -> tuple[dg.AssetsDefinition, dict[str, str]]:
    """Generate a Dagster asset definition for a remittance reports job.

    Args:
        scheduled_job (ScheduledJob): The scheduled job to generate an asset for.

    Returns:
        tuple[dg.AssetsDefinition, dict[str, str]]:
          The generated asset definition and the tags for the asset.
    """
    if not scheduled_job.name or not scheduled_job.base_name:
        name_missing_message: str = (
            f"Scheduled job {scheduled_job} has no name or no base name - both are required. "
            "(code: scheduled-job-invalid)"
        )
        logger.error(name_missing_message)
        raise ValueError(name_missing_message)

    _purpose: str = f"{scheduled_job.purpose.value}" if scheduled_job.purpose else ""
    _audience: str = f"{scheduled_job.audience.value}" if scheduled_job.audience else ""
    _base_name: str = f"{scheduled_job.base_name.value}" if scheduled_job.base_name else ""
    _frequency: str = f"{scheduled_job.frequency.value}" if scheduled_job.frequency else ""
    # The else "" is safe in the _environment variable because we know that the environment field is required - it'll
    # error downstream if it's not present.
    _environment: str = f"{scheduled_job.environment.value}" if scheduled_job.environment else ""
    _schedule_time_zone: str = (
        f"{scheduled_job.schedule_time_zone.value}" if scheduled_job.schedule_time_zone else "America/Chicago"
    )

    tags: dict[str, str] = {
        "com.payit.fine-reports/purpose": _purpose,
        "com.payit.fine-reports/audience": _audience,
        "com.payit.fine-reports/base_name": _base_name,
        "com.payit.fine-reports/frequency": _frequency,
    }

    settings_update_data: dict[str, Any] = {
        "base_name": scheduled_job.base_name,
        "audience": scheduled_job.audience,
        "frequency": scheduled_job.frequency,
        "purpose": scheduled_job.purpose,
    }
    args: Arguments | None = scheduled_job.arguments
    if args is None:
        args_missing_message: str = (
            f"Scheduled job '{scheduled_job.name}' with purpose '{_purpose}' has no arguments - they are required. "
            "(code: scheduled-job-invalid)"
        )
        logger.error(args_missing_message)
        raise ValueError(args_missing_message)

    set_config_value(tags, settings_update_data, "config_path", args.config_path, tag_it=True)
    set_config_value(tags, settings_update_data, "dropbox_path", args.dropbox_path)
    set_config_value(tags, settings_update_data, "enable_delivery", args.enable_delivery, tag_it=True)
    set_config_value(tags, settings_update_data, "end_time", args.end_time, tag_it=True)
    set_config_value(tags, settings_update_data, "file_name_suffix", args.file_name_suffix)
    set_config_value(tags, settings_update_data, "filename_tag", args.filename_tag)
    set_config_value(tags, settings_update_data, "ftp_filepath", args.ftp_filepath)
    set_config_value(tags, settings_update_data, "is_toronto", args.is_toronto, tag_it=True)
    set_config_value(tags, settings_update_data, "needs_documentdb_data", args.needs_documentdb_data, tag_it=True)
    set_config_value(tags, settings_update_data, "report_date", args.report_date, tag_it=True)
    set_config_value(tags, settings_update_data, "report_name", args.report_name, tag_it=True)
    set_config_value(tags, settings_update_data, "save_remittance_data", args.save_remittance_data, tag_it=True)
    set_config_value(tags, settings_update_data, "settlement_date", args.settlement_date)
    set_config_value(tags, settings_update_data, "source_database", args.source_database, tag_it=True)
    set_config_value(tags, settings_update_data, "start_time", args.start_time, tag_it=True)
    set_config_value(tags, settings_update_data, "use_mongo_data", args.use_mongo_data, tag_it=True)
    set_config_value(tags, settings_update_data, "use_snowflake", args.use_snowflake, tag_it=True)
    set_config_value(tags, settings_update_data, "report_folder_path", args.report_folder_path)

    ftp_secret_name: str | None = f"{args.ftp.value}" if args.ftp else None
    if ftp_secret_name:
        tags["com.payit.fine-reports/ftp-secret-name"] = ftp_secret_name

        ftp_env_var_prefix: str = ftp_secret_name.upper().replace("-", "_")

        ftp_host_env_var_key: str = f"{ftp_env_var_prefix}_HOST"
        ftp_port_env_var_key: str = f"{ftp_env_var_prefix}_PORT"
        ftp_username_env_var_key: str = f"{ftp_env_var_prefix}_USERNAME"
        ftp_password_env_var_key: str = f"{ftp_env_var_prefix}_PASSWORD"

        ftp_host_env_var_value: str | None = dg.EnvVar(ftp_host_env_var_key).get_value()
        ftp_port_env_var_value: str | None = dg.EnvVar(ftp_port_env_var_key).get_value()
        ftp_username_env_var_value: str | None = dg.EnvVar(ftp_username_env_var_key).get_value()
        ftp_password_env_var_value: str | None = dg.EnvVar(ftp_password_env_var_key).get_value()

        ftp_env_var_values: list[str | None] = [
            ftp_host_env_var_value,
            ftp_port_env_var_value,
            ftp_username_env_var_value,
            ftp_password_env_var_value,
        ]
        if not all(ftp_env_var_values):
            raise ValueError(
                f"Environment variables {ftp_host_env_var_key}, {ftp_port_env_var_key}, "
                f"{ftp_username_env_var_key}, and {ftp_password_env_var_key} must be set "
                f"for scheduled job {scheduled_job.name}."
            )
        settings_update_data["ftp_host"] = ftp_host_env_var_value
        settings_update_data["ftp_port"] = ftp_port_env_var_value
        settings_update_data["ftp_user"] = ftp_username_env_var_value
        settings_update_data["ftp_password"] = ftp_password_env_var_value

    dbt_asset_dependencies: list[list[str]] = []
    if args.report_name is not None:
        report_name: str = args.report_name
        report_configuration: ReportConfiguration = get_report_configuration(
            report_name=report_name,
            environment=_environment,
        )
        if report_configuration.data_sources is not None:
            report_data_sources: ReportDataSources = report_configuration.data_sources
            if report_data_sources.snowpaw_remittance_models is not None:
                snowpaw_remittance_models: list[SnowpawRemittanceDataSource] = (
                    report_data_sources.snowpaw_remittance_models
                )
                for snowpaw_remittance_model in snowpaw_remittance_models:
                    dbt_asset_dependencies.append(["remittance", snowpaw_remittance_model.model_name])

    @dg.asset(
        owners=[DATA_TEAM_OWNER],
        key=[_base_name, scheduled_job.name],
        deps=dbt_asset_dependencies,
        group_name=_purpose,
        tags=tags,
        description=(
            f"Remittance report job of base name {_base_name}, corresponding to ScheduledJob {scheduled_job.name}."
        ),
    )
    def _remittance_reports_asset(context: dg.AssetExecutionContext, config: FineReportsLegacyConfig) -> dg.Output[Any]:
        """Generate the specified remittance report asset.

        Args:
            context (dg.AssetExecutionContext): The Dagster asset execution context.
            config (FineReportsLegacyConfig): Configuration which may be overridden at time of materialization.

        Returns:
            dg.Output[Any]: The output of the remittance report asset.

        Raises:
            ValueError: If this is a preview job that uses Snowflake and any required DBT model dependencies
                have not been successfully materialized.
        """
        ## Dagster doesn't provide a simple way to get "start time of current Run" inside an asset function, so
        ## we just set it to "now" here (before doing anything else). If this poses issues we will re-evaluate.
        now: datetime = datetime.now(timezone.utc)

        ## Set the job run metadata
        current_run: dg.DagsterRun = context.run

        ## We set these directly on the settings_update_data dictionary because we don't want to expose them as tags
        ## (their values are specific to an individual Run, and not part of the asset or job definition)
        settings_update_data["job_run_name"] = current_run.job_name
        settings_update_data["job_run_id"] = current_run.run_id
        settings_update_data["job_run_creation_timestamp"] = now.isoformat(timespec="milliseconds")

        # set report_date regardless - the Config class uses the same default value as remittance_reports defaults to
        # in settings.toml, so even if a given ScheduledJob doesn't override it, we're okay to set it here
        settings_update_data["report_date"] = config.report_date
        # remittance reports defaults enable_delivery to "false" (string, lowercase). That's the default value for
        # the Config class too. We override it here only if the Config class has the non-default value of True.
        enable_delivery_config_value: str = f"{config.enable_delivery}"
        settings_update_data["enable_delivery"] = f"{enable_delivery_config_value.lower()}"
        # remittance reports defaults save_remittance_data to "false" (string, lowercase). That's the default value for
        # the Config class too. We override it here only if the Config class has the non-default value of True.
        save_remittance_data_config_value: str = f"{config.save_remittance_data}"
        settings_update_data["save_remittance_data"] = f"{save_remittance_data_config_value.lower()}"

        context.log.info(
            f"Executing job {scheduled_job.name} with base name '{_base_name}', audience '{_audience}', "
            f"frequency '{_frequency}', purpose '{_purpose}'."
        )

        ## log the arguments - these do not contain any sensitive information
        args_pretty: str = json.dumps(args.model_dump(), indent=4)
        context.log.info(f"Job Arguments:\n{args_pretty}")

        dagster_config_pretty: str = json.dumps(config.model_dump(), indent=4)
        context.log.info(f"Dagster Config:\n{dagster_config_pretty}")

        from dynaconf import settings as dynaconf_settings

        from config import settings as local_settings

        dynaconf_settings.update(data=local_settings, validate=False, **settings_update_data)

        from entrypoint import main as run_remittance_report_job

        run_remittance_report_job()

        return dg.Output(value=None)

    return _remittance_reports_asset, tags
