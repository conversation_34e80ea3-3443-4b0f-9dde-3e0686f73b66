"""Dagster definitions module.

This module defines the Definitions object which is the main Dagster entrypoint for a code location.
"""

from copy import deepcopy

import dagster as dg
from dagster._core.definitions.unresolved_asset_job_definition import UnresolvedAssetJobDefinition
from dagster_pagerduty import PagerDutyService
from dagster_slack import SlackResource
from payit_data_common.api.models.scheduled_job import ScheduledJob

from fine_reports.dagster.legacy.assets import generate_remittance_reports_asset
from fine_reports.dagster.legacy.batch_reruns import define_rerun_job_for_report_date
from fine_reports.dagster.legacy.configuration import FineReportsLegacyConfig
from fine_reports.dagster.legacy.generators import get_default_schedule_status, get_scheduled_job_resources_for_env
from fine_reports.dagster.sensors.sensors.alerts import alert_sensors
from fine_reports.dagster.sensors.sensors.notifications import notification_sensors
from fine_reports.dagster.utils.environment import validate_environment
from fine_reports.ports.logging import FineReportsLogger, get_logger

logger: FineReportsLogger = get_logger(__name__)

## This call will ensure the Dagster user code Deployment will not start up without all necessary environment variables
validate_environment()


all_legacy_dagster_assets: list[dg.AssetsDefinition] = []
all_legacy_dagster_jobs: list[dg.JobDefinition | UnresolvedAssetJobDefinition] = []
all_legacy_dagster_schedules: list[dg.ScheduleDefinition] = []

all_scheduled_jobs: list[ScheduledJob] = get_scheduled_job_resources_for_env()


scheduled_job: ScheduledJob
for scheduled_job in all_scheduled_jobs:
    if not scheduled_job.name:
        logger.warning("Scheduled job has no name, skipping.")
        continue
    if not scheduled_job.base_name:
        logger.warning("Scheduled job has no base name, skipping.")
        continue
    if scheduled_job.created is False and scheduled_job.enabled is False:
        logger.info(f"Scheduled job {scheduled_job.name} is not created and not enabled, skipping.")
        continue
    _base_name: str = f"{scheduled_job.base_name.value}"
    _purpose: str = scheduled_job.purpose.value if scheduled_job.purpose else ""
    logger.info(f"Scheduled job with name '{scheduled_job.name}' and purpose '{_purpose}'")

    ## Define asset
    asset_definition: dg.AssetsDefinition
    asset_tags: dict[str, str]
    asset_definition, asset_tags = generate_remittance_reports_asset(scheduled_job)
    all_legacy_dagster_assets.append(asset_definition)

    ## define job if it is created
    if scheduled_job.created:
        run_config_settings: dict[str, str | bool] = {}
        if scheduled_job.arguments:
            if scheduled_job.arguments.report_date:
                run_config_settings["report_date"] = scheduled_job.arguments.report_date
            if scheduled_job.arguments.enable_delivery:
                run_config_settings["enable_delivery"] = scheduled_job.arguments.enable_delivery.value == "true"
            if scheduled_job.arguments.save_remittance_data:
                run_config_settings["save_remittance_data"] = (
                    scheduled_job.arguments.save_remittance_data.value == "true"
                )

        op_name: str = f"{_base_name}__{scheduled_job.name}"
        logger.info(f"op config key: {op_name}")
        run_config: dg.RunConfig = dg.RunConfig(
            ops={
                op_name: FineReportsLegacyConfig(**run_config_settings),  # type: ignore[arg-type]
            }
        )

        job_tags: dict[str, str] = deepcopy(asset_tags)

        logger.info(f"Scheduled job tags: {job_tags}")

        asset_selection: dg.AssetSelection = dg.AssetSelection.assets(asset_definition)
        asset_job_definition: UnresolvedAssetJobDefinition = dg.define_asset_job(
            name=scheduled_job.name,
            selection=asset_selection,
            description=(f"Remittance report job '{scheduled_job.name}' with purpose '{_purpose}'."),
            tags=job_tags,
            config=run_config,
        )

        ## define schedule if a cron expression exists
        ## default the status to RUNNING if the job is enabled
        if scheduled_job.schedule_expression and scheduled_job.schedule_time_zone:
            default_schedule_status: dg.DefaultScheduleStatus = get_default_schedule_status(scheduled_job)

            asset_job_schedule_definition: dg.ScheduleDefinition = dg.ScheduleDefinition(
                name=scheduled_job.name,
                description=(f"Remittance report job '{scheduled_job.name}' with purpose '{_purpose}'."),
                job=asset_job_definition,
                cron_schedule=scheduled_job.schedule_expression,
                default_status=default_schedule_status,
                ## the schema restricts this to America/Chicago, which is what we want
                execution_timezone=f"{scheduled_job.schedule_time_zone.value}",
                tags=job_tags,
                run_config=run_config,
            )
            all_legacy_dagster_schedules.append(asset_job_schedule_definition)


## TEMPORARY LIST for recovering from EI2-847
report_dates_to_rerun: list[str] = [
    "2025-06-09",
]

rerun_jobs: list[UnresolvedAssetJobDefinition] = []

report_date: str
for report_date in report_dates_to_rerun:
    rerun_job_definition: UnresolvedAssetJobDefinition = define_rerun_job_for_report_date(report_date)
    rerun_jobs.append(rerun_job_definition)

logger.info(f"Rerun jobs: {len(rerun_jobs)}")

defs: dg.Definitions = dg.Definitions(
    assets=[
        *all_legacy_dagster_assets,
    ],
    jobs=[
        *all_legacy_dagster_jobs,
        *rerun_jobs,
    ],
    schedules=[
        *all_legacy_dagster_schedules,
    ],
    sensors=[
        *alert_sensors,
        *notification_sensors,
    ],
    resources={
        "slack": SlackResource(token=dg.EnvVar("FINE_REPORTS_SLACK_TOKEN")),
        "pagerduty": PagerDutyService(routing_key=dg.EnvVar("FINE_REPORTS_PAGERDUTY_SERVICE_KEY")),
    },
)
"""Dagster definitions."""
