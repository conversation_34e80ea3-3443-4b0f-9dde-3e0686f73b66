"""Database command invoker job definition."""

from typing import override

from config import settings
from fine_reports.datastore.oplog import OplogDatastore
from fine_reports.datastore.oplog.pg import PyGreSQLOplogDatastore
from fine_reports.jobs import FineReportsJob
from fine_reports.ports.logging import FineReportsLogger, get_logger
from fine_reports.utilities.dates import parse_report_date

logger: FineReportsLogger = get_logger(__name__)


class DatabaseCommandInvokerJob(FineReportsJob):
    """Database command invoker job."""

    def __init__(self) -> None:
        self._report_date: str = parse_report_date(settings.report_date)
        self._db_cxn: OplogDatastore = PyGreSQLOplogDatastore()

    @override
    def run(self) -> None:
        """Run the job and invoke a database command."""
        report_name_to_sp_map: dict[str, str] = {
            "beaufort_ptax_settlement_categories": "sp_update_beaufort_ptax_settlement_categories",
            "jackson_ptax_pos_settlement_categories": "sp_update_jackson_ptax_pos_settlement_categories",
            "jackson_ptax_settlement_categories": "sp_update_jackson_ptax_settlement_categories",
        }

        _report_name: str = settings.report_name

        if settings.report_name not in report_name_to_sp_map:
            raise ValueError("Unhandled report name value")
        sp_name: str = report_name_to_sp_map[settings.report_name]

        # call that stored procedure
        # not wrapping in a try:except as the resulting exception messaging is clear and raises the exception
        self._db_cxn.execute(f"CALL {sp_name}('{self._report_date}')")
        logger.info(f"Database command invoker job started with report_date {self._report_date}.")
        logger.info(f"Database command invoker job started with report_name {_report_name}.")
