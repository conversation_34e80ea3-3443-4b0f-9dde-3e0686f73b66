"""Snowflake PAW (SnowPAW) datastore."""

from contextlib import contextmanager
from functools import lru_cache
import json
from pathlib import Path
from typing import Any, Generator

from jinja2 import Environment, PackageLoader, Template
from jinjasql import JinjaSql
from snowflake.sqlalchemy import URL
from sqlalchemy import Connection, CursorResult, Engine, TextClause, create_engine, text

from config import settings
from fine_reports.configuration.models.enums import BaseName, Setting, SourceDatabase
from fine_reports.ports.logging import FineReportsLogger, get_logger
from fine_reports.utilities.kubernetes import is_running_in_kubernetes
from payit_remittance_report.__about__ import __version__

logger: FineReportsLogger = get_logger(__name__)


@contextmanager
def set_snowflake_query_tag(
    connection: Connection, query_name: str, query_params: dict[str, Any]
) -> Generator[None, None, None]:
    """Set the query tag for a Snowflake query and unset it afterwards.

    Args:
        connection (Connection): The Snowflake connection.
        query_name (str): The name of the query.
        query_params (dict): Query parameters.

    Yields:
        None: This context manager does not yield a value. It sets the query tag before the yield and unsets it after
        the block completes.
    """
    keys_for_query_tag: set[str] = {"report_name", "base_name"}
    query_tag_object: dict[str, Any] = {k: v for k, v in query_params.items() if k in keys_for_query_tag}
    query_tag_object["query_name"] = query_name
    query_tag_object["project_name"] = "remittance_reports"
    query_tag_object["project_version"] = __version__
    query_tag: str = json.dumps(query_tag_object, separators=(",", ":"))

    try:
        connection.execute(text(f"ALTER SESSION SET QUERY_TAG='{query_tag}';"))
        yield
    finally:
        connection.execute(text("ALTER SESSION UNSET QUERY_TAG;"))


@lru_cache
def get_jinja_sql() -> JinjaSql:
    """Return a JinjaSql object for templating SQL queries with macros and filters preloaded.

    This function is cached to avoid creating a new JinjaSql object every time it is called.

    Returns:
        JinjaSql: JinjaSql object.
    """
    # Create the JinjaSql object
    jinja_sql: JinjaSql = JinjaSql(param_style="named")

    # Access the JinjaSql environment
    env: Environment = jinja_sql.env

    # Set up a PackageLoader for the macros directory
    loader = PackageLoader("fine_reports.queries.snowpaw", "_macros")
    env.loader = loader

    # Preload macros into the environment
    macro_name: str
    for macro_name in loader.list_templates():
        template: Template = env.get_template(macro_name)  # Load the macro template
        env.globals.update(template.module.__dict__)  # Add macros to globals

    return jinja_sql


def prepare_query_from_file(query_filepath: Path, **query_params) -> tuple[str, dict[str, Any]]:
    """Build a query from a Jinja template and parameters.

    Args:
        query_filepath (str): The path to the SQL file containing the query definition
        query_params (dict): Query parameters.

    Returns:
        tuple[str, dict]: The query and bind parameters
    """
    if not query_filepath.exists():
        query_name: str = query_filepath.name.replace(".jinja.sql", "")
        raise ValueError(f"Query '{query_name}' not found in the Snowflake PAW queries module.")
    raw_query: str = query_filepath.read_text()

    query: str
    bind_params: dict[str, Any]
    query, bind_params = get_jinja_sql().prepare_query(raw_query, query_params)
    return query, bind_params


def prepare_query(query_name: str, **query_params) -> tuple[str, dict[str, Any]]:
    """Build a query from a Jinja template and parameters.

    Args:
        query_name (str): The name of the query to run.
        query_params (dict): Query parameters.

    Returns:
        tuple[str, dict[str, Any]]: The query and bind parameters
    """
    repository_root_dir: Path = Path(__file__).parent.parent.parent.parent
    snowpaw_query_dir: Path = repository_root_dir / "fine_reports/queries/snowpaw"
    base_name: str = settings.base_name
    query_filepath: Path = snowpaw_query_dir / f"{base_name}/{query_name}.jinja.sql"
    if not query_filepath.exists():
        raise ValueError(f"Query '{query_name}' not found in the Snowflake PAW queries module.")
    return prepare_query_from_file(query_filepath, **query_params)


class SnowpawDatastore:
    """Snowflake PAW (SnowPAW) datastore reader implementation.

    Some of the implementation details here are sourced from
    https://docs.snowflake.com/en/developer-guide/python-connector/sqlalchemy#verifying-your-installation
    (particularly the `SELECT current_version()` query for connection testing).
    """

    def __init__(self) -> None:
        """Initialize the Snowflake PAW (SnowPAW) datastore reader.

        Currently (2024-07-29 / DA-7460) we only support authentication via a service account username and password. We
        plan to add support for developers (who don't have service account access - that's restricted to the Data team)
        to authenticate to Snowflake via Okta when running remittance reports locally.
        """
        if not self._can_use_snowflake():
            raise ValueError("Snowflake use is not allowed per the configured settings.")
        self._engine: Engine = self._get_engine()
        self._jinjasql: JinjaSql = JinjaSql(param_style="named")

    def named_query(self, query_name: str, **query_params) -> CursorResult[Any]:
        """Execute a named query and return the results.

        Note that the following parameters are automatically added to all queries - you do not need to provide them
        when calling this method:

            - database_name
            - base_name
            - report_name (empty string if the base_name does not require a report_name)

        Args:
            query_name (str): The name of the query to run.
            params (dict): Query parameters.

        Returns:
            QueryResult: Query results
        """
        base_name: BaseName = settings.base_name
        extra_params: dict[str, str] = {
            "database_name": settings.snowflake_database,
            "base_name": str(base_name.value),
        }
        if base_name in {BaseName.FINANCIAL_REMITTANCE, BaseName.EBILLING_REMITTANCE, BaseName.AUTOPAY_REMITTANCE}:
            extra_params["report_name"] = settings.report_name

        query_params.update(extra_params)

        query, bind_params = prepare_query(query_name, **query_params)

        query_sql: TextClause = text(query)

        logger.info(f"Executing Snowflake PAW query '{query_name}' with parameters: {bind_params}")
        logger.info(f"Query SQL: \n{query_sql}")

        ## Uncomment the next four lines to print the raw, compiled SQL query for debugging
        # compiled = query_sql.bindparams(**bind_params).compile(
        #     dialect=self._engine.dialect,
        #     compile_kwargs={"literal_binds": True},
        # )
        # logger.info(f"Raw, compiled query: \n{compiled}")

        with self._engine.begin() as connection:
            with set_snowflake_query_tag(connection, query_name, query_params):
                return connection.execute(query_sql, bind_params)

    def _get_engine(self) -> Engine:
        """Get a SQLAlchemy Engine for the Snowflake connection.

        If Okta authentication is allowed, prefer that. Otherwise, use the service account credentials.

        Returns:
            Engine: The SQLAlchemy engine.
        """
        okta_username_setting: str = f"{Setting.SNOWFLAKE_OKTA_USERNAME}"
        if okta_username_setting in settings and settings.snowflake_okta_username and not is_running_in_kubernetes():
            logger.info("Using Okta authentication for Snowflake connection.")
            return self._get_okta_engine()
        logger.info("Using service account username/password authentication for Snowflake connection.")
        return self._get_service_account_engine()

    def _get_okta_engine(self) -> Engine:
        """Get the SQLAlchemy engine using a service account credential (username and password authentication).

        Returns:
            Engine: The SQLAlchemy engine.
        """
        if is_running_in_kubernetes():
            raise ValueError("Okta authentication is not supported when running in Kubernetes.")
        snowflake_okta_username: str = settings.snowflake_okta_username
        account_identifier: str = settings.snowflake_account_identifier
        database: str = settings.snowflake_database
        role: str = settings.snowflake_okta_role
        return create_engine(
            URL(
                account=account_identifier,
                user=snowflake_okta_username,
                authenticator="externalbrowser",
                database=database,
                role=role,
            )
        )

    def _get_service_account_engine(self) -> Engine:
        """Get the SQLAlchemy engine using a service account credential (username and password authentication).

        The Snowflake SQLAlchemy conection string format is documented here:
        https://docs.snowflake.com/en/developer-guide/python-connector/sqlalchemy#connection-parameters

        Returns:
            Engine: The SQLAlchemy engine.
        """
        service_account_username: str = settings.snowflake_service_account_username
        service_account_password: str = settings.snowflake_service_account_password
        account_identifier: str = settings.snowflake_account_identifier
        database: str = settings.snowflake_database
        role: str = "svc_remittance_reports_role"
        warehouse: str = "svc_remittance_reports_wh"
        connection_string: str = (
            f"snowflake://{service_account_username}:{service_account_password}@{account_identifier}"
            f"/{database}?warehouse={warehouse}&role={role}"
        )
        return create_engine(connection_string)

    def _can_use_snowflake(self) -> bool:
        """Check if we can use Snowflake.

        Allow this Snowflake datastore to be used only in two scenarios:
        - if settings.use_snowflake is True
        - if we're running a database connection test that targets the SnowPAW

        This method provides a guard against other parts of the remittance_reports codebase attempting to use Snowflake
        in ways that we might not be able to anticipate.

        Returns:
            bool: True if we can use Snowflake, False otherwise.
        """
        if bool(settings.use_snowflake):
            return True
        if settings.base_name == "database_connection_test" or settings.base_name == BaseName.DATABASE_CONNECTION_TEST:
            if settings.source_database == "snowpaw" or settings.source_database == SourceDatabase.SNOWPAW:
                return True
        return False
