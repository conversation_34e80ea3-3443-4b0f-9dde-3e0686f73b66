"""ConfigMixin implementation."""

from typing import Any

from payit_data_common.api.models.enums import Purpose
from payit_data_common.api.models.report_configuration import ReportConfiguration

from fine_reports.configuration.io import get_report_configuration
from fine_reports.settings import RunSettings, get_run_settings


class ConfigMixin:
    """Configuration mixin, providing access to both runtime settings and report configuration."""

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        """Initialize the ConfigMixin.

        Args:
            *args: Additional arguments.
            **kwargs: Additional keyword arguments.
        """
        self._run_settings: RunSettings = get_run_settings()
        report_name: str = self._run_settings.report_name()
        environment: str = self._run_settings.environment()
        self._report_configuration: ReportConfiguration = get_report_configuration(
            report_name=report_name,
            environment=environment,
        )

    @property
    def run_settings(self) -> RunSettings:
        """Get the run settings.

        Returns:
            RunSettings: The run settings.
        """
        return self._run_settings

    @property
    def report_configuration(self) -> ReportConfiguration:
        """Get the report configuration.

        Returns:
            ReportConfiguration: The report configuration.

        Raises:
            fine_reports.exceptions.MissingReportConfigurationException: If the report configuration is not found.
        """
        return self._report_configuration

    def is_snowflake_preview(self) -> bool:
        """Return True if the report is running in Preview mode and has enabled Snowflake, False otherwise.

        Returns:
            bool: True if the report is running in Preview mode and has enabled Snowflake, False otherwise.
        """
        return self.run_settings.purpose() == Purpose.PREVIEW and self.run_settings.use_snowflake()
