[sqlfluff]
dialect = snowflake
max_line_length = 120
nocolor = False
templater = jinja
exclude_rules = L031, L034, RF04, ST03
large_file_skip_byte_limit = 0
warn_unused_ignores = True

[sqlfluff:templater:jinja]
apply_dbt_builtins = False
library_path = tests/sqlfluff_helpers
load_macros_from_path = fine_reports/queries/snowpaw/_macros

[sqlfluff:templater:jinja:context]
# these are sample values - they're used as 'dummy' replacements so that the
# SQL can be accurately linted. These values can be set to anything as long
# as they have the right shape/type (e.g., use an integer for an integer
# variable, etc).

# these three variables get provided to every query
database_name = 'dev_crucible'
base_name = 'financial_remittance'
report_name = 'test_report'

# These are the variables that are used in the queries.
# Any time you add a new SQL query or update one to use a new variable, add a "dummy"/"sample" value of that variable here.
# (let's try to keep these in alphabetical order)
app_names = ['PayAnokaCounty']
begin_date = '2024-10-10'
client_emails_to_exclude = ['<EMAIL>']
end_date = '2024-10-10'
end_timestamp = "'2024-12-18 21:00:00.000000'"
enrollment_date_column = 'updated_at_date_est'
expiration_date = '2024-12-31'
merchant_transaction_ids = ['1234567890', '0987654321']
param_date = '2025-04-16'
param_timezone = 'US/Central'
remittance_date = '2025-04-16'
remittance_disbursement_rows = [("'test_report'", 1, "'tenant1'", "'txn1'", "'line1'", "'2025-04-01'", 100.00, "'credit'", "'2025-04-01 12:00:00'", "'disb1'", "'2025-04-01 12:00:00'", "'2025-04-01 12:00:00'", "'proc1'", "'hash1'"), ("'test_report'", 2, "'tenant2'", "'txn2'", "'line2'", "'2025-04-02'", 200.00, "'debit'", "'2025-04-02 12:00:00'", "'disb2'", "'2025-04-02 12:00:00'", "'2025-04-02 12:00:00'", "'proc2'", "'hash2'")]
report_end_date = '2024-09-10'
report_start_date = '2024-09-09'
service_offering_id = 'gr_water'
service_offering_ids = ['gr_water']
start_timestamp = "'2024-12-17 21:00:00.000000'"
tags_list = ['ebilling', 'ebilling_trial']
transaction_ids = ['f641e85b-c088-4baa-8e10-d6a9c6831875']
updated_at_column = 'updated_at_est'

[sqlfluff:indentation]
indent_unit = space
indented_ctes = True
indented_joins = True
indented_using_on = True
tab_space_size = 4

[sqlfluff:layout:type:comma]
line_position = leading

[sqlfluff:rules:aliasing.column]
aliasing = explicit

[sqlfluff:rules:aliasing.expression]
allow_scalar = False

[sqlfluff:rules:aliasing.table]
aliasing = explicit

[sqlfluff:rules:ambiguous.join]
fully_qualify_join_types = both

[sqlfluff:rules:capitalisation.functions]
extended_capitalisation_policy = lower

[sqlfluff:rules:capitalisation.identifiers]
extended_capitalisation_policy = lower
unquoted_identifiers_policy = all

[sqlfluff:rules:capitalisation.keywords]
capitalisation_policy = upper

[sqlfluff:rules:capitalisation.literals]
capitalisation_policy = upper

[sqlfluff:rules:capitalisation.types]
extended_capitalisation_policy = upper

[sqlfluff:rules:convention.terminator]
multiline_newline = False
require_final_semicolon = True

[sqlfluff:rules:layout.long_lines]
ignore_comment_clauses = True
ignore_comment_lines = True

[sqlfluff:rules:layout.select_targets]
wildcard_policy = multiple

[sqlfluff:rules:references.from]
force_enable = False

[sqlfluff:rules:references.quoting]
prefer_quoted_identifiers = False
