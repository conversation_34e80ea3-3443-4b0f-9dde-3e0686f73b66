from time import time

from opentelemetry import trace
from opentelemetry.trace import Tracer

from fine_reports.ports.logging import FineReportsLogger, get_logger
from payit_remittance_report.custom_integration.aldine_tx_isd_property_tax_excel import (
    AldinePropertyTaxRemittanceExcel,
)
from payit_remittance_report.custom_integration.anoka_child_care_services_monthly_recap import (
    AnokaChildCareServicesMonthlyRecap,
)
from payit_remittance_report.custom_integration.anoka_corrections_fees_monthly_recap import (
    AnokaCorrectionsFeesMonthlyRecap,
)
from payit_remittance_report.custom_integration.anoka_county_property_tax_excel import (
    AnokaPropertyTaxRemittanceExcel,
)
from payit_remittance_report.custom_integration.anoka_county_vital_records_forms_excel import (
    AnokaCountyVitalRecordsRemittanceExcel,
)
from payit_remittance_report.custom_integration.anoka_economic_assistance_overpayments_monthly_recap import (
    AnokaEconomicAssistanceOverpaymentsMonthlyRecap,
)
from payit_remittance_report.custom_integration.anoka_highway_permits_monthly_recap import (
    AnokaHighwayPermitsMonthlyRecap,
)
from payit_remittance_report.custom_integration.anoka_medical_examiner_records_monthly_recap import (
    AnokaMedicalExaminerRecordsMonthlyRecap,
)
from payit_remittance_report.custom_integration.anoka_miscellaneous_payment_monthly_recap import (
    AnokaMiscellaneousPaymentMonthlyRecap,
)
from payit_remittance_report.custom_integration.beaufort_enrollment_monthly_recap import BeaufortEnrollmentMonthlyRecap
from payit_remittance_report.custom_integration.beaufort_enrollment_monthly_recap_v2 import (
    BeaufortEnrollmentV2MonthlyRecap,
)
from payit_remittance_report.custom_integration.beaufort_monthly_v2_recap import BeaufortTaxMonthlyV2Recap
from payit_remittance_report.custom_integration.beaufort_property_tax_remittance import BeaufortImportTaxRemittance
from payit_remittance_report.custom_integration.beaufort_ptax_import import BeaufortPtaxImport
from payit_remittance_report.custom_integration.beaufort_ptax_installment_remittance import (
    BeaufortPtaxInstallmentRemittance,
)
from payit_remittance_report.custom_integration.beaufort_ptax_remittance import BeaufortPtaxRemittance
from payit_remittance_report.custom_integration.buffalo_ny_adjudication_import import BuffaloNYAdjudicationImport
from payit_remittance_report.custom_integration.buffalo_ny_dog_licenses_import import BuffaloNYDogLicensesImport
from payit_remittance_report.custom_integration.buffalo_ny_general_billing_import import BuffaloNYGeneralBillingImport
from payit_remittance_report.custom_integration.buffalo_ny_parking_import import BuffaloNYParkingImport
from payit_remittance_report.custom_integration.buffalo_ny_parking_unlisted_import import BuffaloNYParkingUnlistedImport
from payit_remittance_report.custom_integration.buffalo_ny_property_tax_import import BuffaloNYPtaxImport
from payit_remittance_report.custom_integration.buffalo_ny_user_fees_import import BuffaloNYUserFeesImport
from payit_remittance_report.custom_integration.buncombe_nc_property_tax_monthly_recap import (
    BuncombeNcPropertyTaxMonthlyRecap,
)
from payit_remittance_report.custom_integration.charleston_aviation_express_monthly_recap import (
    CharlestonAviationExpressMonthlyRecap,
)
from payit_remittance_report.custom_integration.co_dl_cash_receipts_remittance import (
    CODLCashReceiptsFinancialRemittance,
)
from payit_remittance_report.custom_integration.co_dl_fee_disbursement_monthly_recap import (
    CODLFeeDisbursementFinancialRemittance,
)
from payit_remittance_report.custom_integration.co_dl_payment_details_remittance import (
    CODLPaymentDetailsFinancialRemittance,
)
from payit_remittance_report.custom_integration.co_dl_receivables_remittance import CODLReceivablesFinancialRemittance
from payit_remittance_report.custom_integration.co_dmv_remittance import CODMVFinancialRemittance
from payit_remittance_report.custom_integration.columbia_mo_citations_monthly_recap import (
    ColumbiaMOCitationsMonthlyRecap,
)
from payit_remittance_report.custom_integration.columbia_mo_gen_pay_home_energy_import import (
    ColumbiaMOGenPayHomeEnergyImport,
)
from payit_remittance_report.custom_integration.columbia_mo_gen_pay_import import ColumbiaMOGenPayImport
from payit_remittance_report.custom_integration.columbia_mo_general_billing_monthly_recap import (
    ColumbiaMoGeneralBillingMonthlyRecap,
)
from payit_remittance_report.custom_integration.columbia_mo_home_energy_loans_monthly_recap import (
    ColumbiaMoHomeEnergyLoansMonthlyRecap,
)
from payit_remittance_report.custom_integration.columbia_mo_utilities_billing import ColumbiaMoUtilitiesBilling
from payit_remittance_report.custom_integration.columbia_mo_utilities_import import ColumbiaMoUtilitiesImport
from payit_remittance_report.custom_integration.columbia_mo_utilities_monthly_recap import (
    ColumbiaMoUtilitiesMonthlyRecap,
)
from payit_remittance_report.custom_integration.columbia_mo_utilities_request_forms_import import (
    ColumbiaMoUtilitiesRequestFormsImport,
)
from payit_remittance_report.custom_integration.des_moines_wa_marina_import import DesMoinesWAImport
from payit_remittance_report.custom_integration.douglas_tax_import import DouglasTaxImport
from payit_remittance_report.custom_integration.douglas_tax_remittance import DouglasTaxRemittance
from payit_remittance_report.custom_integration.essex_on_property_tax_import import EssexOntarioPtaxImport
from payit_remittance_report.custom_integration.essex_on_utilities_import import EssexOntarioUtilitiesImport
from payit_remittance_report.custom_integration.fl_sunpass_daily_import import FLSunPassDailyImport
from payit_remittance_report.custom_integration.fl_sunpass_financial_remittance import FLSunPassFinancialRemittance
from payit_remittance_report.custom_integration.florida_dmv_monthly_email_campaign import FloridaDMVMonthlyCampaign
from payit_remittance_report.custom_integration.florida_dmv_remittance import FLDMVRemittance
from payit_remittance_report.custom_integration.fort_smith_utilities_monthly_recap import FortSmithUtilitiesMonthlyRecap
from payit_remittance_report.custom_integration.fort_smith_utilities_payments_import import (
    FortSmithUtilitiesPaymentsImport,
)
from payit_remittance_report.custom_integration.gr_ar_import import GRARImport
from payit_remittance_report.custom_integration.gr_ar_import_v3 import GRARImportV3
from payit_remittance_report.custom_integration.gr_cdl_financial_remittance import GRCDLRemittance
from payit_remittance_report.custom_integration.gr_mr_import import GRMRImport
from payit_remittance_report.custom_integration.gr_parking_financial_remittance import GRParkingRemittance
from payit_remittance_report.custom_integration.gr_parking_import import GRParkingImport
from payit_remittance_report.custom_integration.gr_property_tax_end_of_month_midday_financial_remittance import (
    GRPropertyTaxEndOfMonthMiddayRemittance,
)
from payit_remittance_report.custom_integration.gr_property_tax_end_of_month_midnight_financial_remittance import (
    GRPropertyTaxEndOfMonthMidnightRemittance,
)
from payit_remittance_report.custom_integration.gr_property_tax_remittance import GRPropertyTaxRemittance
from payit_remittance_report.custom_integration.gr_refuse_financial_remittance import GRRefuseRemittance
from payit_remittance_report.custom_integration.gr_refuse_import import GRRefuseImport
from payit_remittance_report.custom_integration.gr_water_financial_remittance import GRWaterRemittance
from payit_remittance_report.custom_integration.gr_water_import import GRWaterImport
from payit_remittance_report.custom_integration.gr_water_import_v2 import GRWaterImportV2
from payit_remittance_report.custom_integration.guilford_property_tax_monthly_recap import (
    GuilfordPropertyTaxMonthlyRecap,
)
from payit_remittance_report.custom_integration.guilford_ptax_remittance import GuilfordPTaxRemittance
from payit_remittance_report.custom_integration.jackson_property_tax_remittance import JacksonPropertyTax
from payit_remittance_report.custom_integration.kansas_cic_int_property_tax_remittance import (
    KansasCICIntPropertyTaxRemittance,
)
from payit_remittance_report.custom_integration.kck_tax_remittance import KCKTaxRemittance
from payit_remittance_report.custom_integration.kdhe_ccl_remittance import KDHECCLRemittance
from payit_remittance_report.custom_integration.kent_county_pos_courts_remittance import KentCountyPosRemittance
from payit_remittance_report.custom_integration.kent_county_pos_monthly_recap import KentCountyPosMonthlyRecap
from payit_remittance_report.custom_integration.kent_county_property_tax_monthly_recap import (
    KentCountyPropertyTaxMonthlyRecap,
)
from payit_remittance_report.custom_integration.kent_muni_pay_monthly_recap import KentCountyMuniPayMonthlyRecap
from payit_remittance_report.custom_integration.kent_roads_commission_monthly_recap import (
    KentCountyRoadsCommissionMonthlyRecap,
)
from payit_remittance_report.custom_integration.knox_county_court_pos_financial_remittance import (
    KnoxCountyCourtPosFinancialRemittance,
)
from payit_remittance_report.custom_integration.knox_county_criminal_court_financial_remittance import (
    KnoxCountyCriminalCourtFinancialRemittance,
)
from payit_remittance_report.custom_integration.knox_county_criminal_court_import import KnoxCountyCriminalCourtImport
from payit_remittance_report.custom_integration.knox_county_criminal_express_financial_remittance import (
    KnoxCountyCriminalExpressFinancialRemittance,
)
from payit_remittance_report.custom_integration.knox_county_criminal_sessions_financial_remittance import (
    KnoxCountyCriminalSessionsFinancialRemittance,
)
from payit_remittance_report.custom_integration.knox_county_criminal_sessions_import import (
    KnoxCountyCriminalSessionsImport,
)
from payit_remittance_report.custom_integration.knox_county_fourth_circuit_express_financial_remittance import (
    KnoxCountyFourthCircuitExpressFinancialRemittance,
)
from payit_remittance_report.custom_integration.knox_county_fourth_circuit_financial_remittance import (
    KnoxCountyFourthCircuitFinancialRemittance,
)
from payit_remittance_report.custom_integration.knox_county_fourth_circuit_import import KnoxCountyFourthCircuitImport
from payit_remittance_report.custom_integration.knox_county_general_session_express_financial_remittance import (
    KnoxCountyGeneralSessionExpressFinancialRemittance,
)
from payit_remittance_report.custom_integration.ks_cdl_financial_remittance import KSCDLRemittance
from payit_remittance_report.custom_integration.ks_dl_coa_financial_remittance import KSDLCoARemittance
from payit_remittance_report.custom_integration.ks_dl_financial_remittance import KSDLRemittance
from payit_remittance_report.custom_integration.lansing_mi_building_safety_monthly_recap import (
    LansingMiBuildingSafetyMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_building_safety_pos_monthly_recap import (
    LansingMiBuildingSafetyPOSMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_city_attorney_monthly_recap import (
    LansingMiCityAttorneyMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_clerks_office_monthly_recap import (
    LansingMiClerksOfficeMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_clerks_office_pos_monthly_recap import (
    LansingMiClerksOfficePOSMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_code_compliance_monthly_recap import (
    LansingMiCodeComplianceMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_code_compliance_pos_monthly_recap import (
    LansingMiCodeCompliancePOSMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_fire_dept_import import LansingMiFireDeptImport
from payit_remittance_report.custom_integration.lansing_mi_fire_dept_integrated_monthly_recap import (
    LansingMiFireDeptIntegratedMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_fire_dept_monthly_recap import LansingMiFireDeptMonthlyRecap
from payit_remittance_report.custom_integration.lansing_mi_fire_dept_pos_monthly_recap import (
    LansingMiFireDeptPOSMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_income_tax_monthly_recap import (
    LansingMiIncomeTaxMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_miscellaneous_payables_monthly_recap import (
    LansingMiMiscellaneousPayablesMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_police_dept_monthly_recap import (
    LansingMiPoliceDeptMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_police_dept_pos_monthly_recap import (
    LansingMiPoliceDeptPOSMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_property_tax_import import LansingMIPropertyTaxImport
from payit_remittance_report.custom_integration.lansing_mi_property_tax_monthly_recap import (
    LansingPropertyTaxMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_public_service_monthly_recap import (
    LansingMiPublicServiceMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_public_service_pos_monthly_recap import (
    LansingMiPublicServicePOSMonthlyRecap,
)
from payit_remittance_report.custom_integration.lansing_mi_refuse_forms_import import LansingMIRefuseFormsImport
from payit_remittance_report.custom_integration.lansing_mi_refuse_import import LansingMIRefuseImport
from payit_remittance_report.custom_integration.lansing_mi_treasury_pos_monthly_recap import (
    LansingMiTreasuryPOSMonthlyRecap,
)
from payit_remittance_report.custom_integration.lee_county_nc_property_tax_import import LeeCountyNCPtaxImport
from payit_remittance_report.custom_integration.memphis_tax_import import MemphisTaxImport
from payit_remittance_report.custom_integration.missouri_outdoors_remittance import MissouriOutdoorsRemittance
from payit_remittance_report.custom_integration.mycabco_property_tax_monthly_recap import MyCabCoPropertyTaxMonthlyRecap
from payit_remittance_report.custom_integration.nc_commercial_vehicle_citation_financial_remittance import (
    NCCommercialVehicleCitationRemittance,
)
from payit_remittance_report.custom_integration.nc_commercial_vehicle_citation_monthly_recap import (
    NCCommercialVehicleCitationMonthlyRecap,
)
from payit_remittance_report.custom_integration.nc_dl_return_monthly_recap import NCDLReturnMonthlyRecap
from payit_remittance_report.custom_integration.nc_ns_express_monthly_recap import NcNsExpressMonthlyRecap
from payit_remittance_report.custom_integration.ncdl_remittance import NCDLRemittance
from payit_remittance_report.custom_integration.ncdmv_civil_penalty_license_and_theft_monthly_recap import (
    NCDMVCpAhsMonthlyRecap,
)
from payit_remittance_report.custom_integration.ncdmv_crash_reports_monthly_recap import NCDMVCrashReportsMonthlyRecap
from payit_remittance_report.custom_integration.ncdmv_crash_reports_remittance import NCDMVCrashReportsRemittance
from payit_remittance_report.custom_integration.ncdmv_driveway_permits_monthly_recap import (
    NCDMVDrivewayPermitsMonthlyRecap,
)
from payit_remittance_report.custom_integration.ncdmv_driveway_permits_remittance import NCDMVDrivewayPermitsRemittance
from payit_remittance_report.custom_integration.ncdmv_driving_record_monthly_recap import NCDMVDrivingRecordMonthlyRecap
from payit_remittance_report.custom_integration.ncdmv_driving_record_remittance import NCDMVDrivingRecordRemittance
from payit_remittance_report.custom_integration.ncdmv_handicap_placard_monthly_recap import (
    NCDMVHandicapPlacardMonthlyRecap,
)
from payit_remittance_report.custom_integration.ncdmv_handicap_placard_remittance import NCDMVHandicapPlacardRemittance
from payit_remittance_report.custom_integration.ncdmv_hearing_fee_license_and_theft_monthly_recap import (
    NCDMVHfAhsMonthlyRecap,
)
from payit_remittance_report.custom_integration.ncdmv_hearing_fee_license_and_theft_remittance import (
    NCDMVHfAhsRemittance,
)
from payit_remittance_report.custom_integration.ncdmv_hearing_fee_lite_monthly_recap import NCDMVHFLitesMonthlyRecap
from payit_remittance_report.custom_integration.ncdmv_hearing_fee_lite_remittance import NCDMVHFLitesRemittance
from payit_remittance_report.custom_integration.ncdmv_hearing_fee_monthly_recap import NCDMVHearingFeeMonthlyRecap
from payit_remittance_report.custom_integration.ncdmv_hearing_fee_remittance import NCDMVHearingFeeRemittance
from payit_remittance_report.custom_integration.ncdmv_lapse_payment_financial_remittance import (
    NCDMVLapsePaymentRemittance,
)
from payit_remittance_report.custom_integration.ncdmv_lapse_payment_monthly_recap import NCDMVLapsePaymentMonthlyRecap
from payit_remittance_report.custom_integration.ncdmv_persona_identity_verification import (
    NCDMVPersonaIdentityVerificationRemittance,
)
from payit_remittance_report.custom_integration.ncdmv_remittance import NCDMVRemittance
from payit_remittance_report.custom_integration.ncdmv_return_monthly_recap import NCDMVReturnMonthlyRecap
from payit_remittance_report.custom_integration.ncns_remittance import NCNSRemittance
from payit_remittance_report.custom_integration.nj_isf_tolls_payments_import import NJISF
from payit_remittance_report.custom_integration.nj_isf_tolls_remittance import NJISFRemittance
from payit_remittance_report.custom_integration.olive_branch_courts_excel import OliveBranchCourtsExcel
from payit_remittance_report.custom_integration.olive_branch_courts_express_excel import OliveBranchCourtsExpressExcel
from payit_remittance_report.custom_integration.olive_branch_courts_express_monthly_recap import (
    OliveBranchCourtsExpressMonthlyRecap,
)
from payit_remittance_report.custom_integration.olive_branch_courts_monthly_recap import OliveBranchCourtsMonthlyRecap
from payit_remittance_report.custom_integration.olive_branch_courts_payments_import import (
    OliveBranchCourtsPaymentsImport,
)
from payit_remittance_report.custom_integration.olive_branch_courts_pos_excel import OliveBranchCourtsPosExcel
from payit_remittance_report.custom_integration.olive_branch_courts_pos_monthly_recap import (
    OliveBranchCourtsPosMonthlyRecap,
)
from payit_remittance_report.custom_integration.olive_branch_utilities_express_monthly_recap import (
    OliveBranchUtilitiesExpressMonthlyRecap,
)
from payit_remittance_report.custom_integration.olive_branch_utilities_monthly_recap import (
    OliveBranchUtilitiesMonthlyRecap,
)
from payit_remittance_report.custom_integration.olive_branch_utilities_payments_import import (
    OliveBranchUtilitiesPaymentsImport,
)
from payit_remittance_report.custom_integration.olive_branch_utilities_pos_monthly_recap import (
    OliveBranchUtilitiesPosMonthlyRecap,
)
from payit_remittance_report.custom_integration.one_alabama_tanf_monthly import OneAlabamaTanfMonthly
from payit_remittance_report.custom_integration.orange_county_ptax_monthly_financial_remittance import (
    OCNCMonthlyRemittance,
)
from payit_remittance_report.custom_integration.orange_county_ptax_remittance import OCNCPTaxRemittance
from payit_remittance_report.custom_integration.outdoors_arkansas_remittance import ArkansasOutdoorsRemittance
from payit_remittance_report.custom_integration.palm_beach_county_alacarte_monthly_recap import (
    PalmBeachCountyAlacarteMonthlyRecap,
)
from payit_remittance_report.custom_integration.palm_beach_county_courts_enrollment_weekly_recap import (
    PalmBeachCountyCourtsEnrollmentWeeklyRecap,
)
from payit_remittance_report.custom_integration.palm_beach_county_courts_monthly_recap import (
    PalmBeachCountyCourtsMonthlyRecap,
)
from payit_remittance_report.custom_integration.palm_beach_county_criminal_monthly_recap import (
    PalmBeachCountyCriminalMonthlyRecap,
)
from payit_remittance_report.custom_integration.palm_beach_county_marriage_license_monthly_recap import (
    PalmBeachCountyMarriageLicenseMonthlyRecap,
)
from payit_remittance_report.custom_integration.palm_beach_county_mediation_monthly_recap import (
    PalmBeachCountyMediationMonthlyRecap,
)
from payit_remittance_report.custom_integration.palm_beach_county_misc_monthly_recap import (
    PalmBeachCountyMiscMonthlyRecap,
)
from payit_remittance_report.custom_integration.palm_beach_county_selfservice_monthly_recap import (
    PalmBeachCountySelfServiceMonthlyRecap,
)
from payit_remittance_report.custom_integration.payit_ebilling_email_sends import (
    PayItEbillingEmailSends,
)
from payit_remittance_report.custom_integration.payit_marketing_ncdmv_vehicle_expirations_import import (
    PayItMarketingNCDMVVehicleExpirationsImport,
)
from payit_remittance_report.custom_integration.pitt_county_nc_property_tax_import import PittCountyNCPropertyTaxImport
from payit_remittance_report.custom_integration.pitt_county_register_of_deeds_pos_monthly_recap import (
    PittCountyRegisterOfDeedsMonthlyRecap,
)
from payit_remittance_report.custom_integration.pitt_nc_solid_waste_import import PittNCSoldWasteImport
from payit_remittance_report.custom_integration.pueblo_co_property_tax_payments_import import (
    PuebloCoPropertyTaxPaymentsImport,
)
from payit_remittance_report.custom_integration.shawnee_property_tax_monthly_recap import ShawneePropertyTaxMonthlyRecap
from payit_remittance_report.custom_integration.shelby_remittance import ShelbyRemittance
from payit_remittance_report.custom_integration.shelby_remittance_v2 import ShelbyRemittanceV2
from payit_remittance_report.custom_integration.shelby_tax_monthly_billing import ShelbyTaxMonthlyBilling
from payit_remittance_report.custom_integration.smith_property_tax_import import SmithPropertyTaxImport
from payit_remittance_report.custom_integration.southaven_ms_utilities_payments_import import (
    SouthavenUtilitiesPaymentsImport,
)
from payit_remittance_report.custom_integration.stl_business_license_application_payment_monthly_recap import (
    StlBusinessLicenseApplicationPaymentMonthlyRecap,
)
from payit_remittance_report.custom_integration.stl_business_license_gbl_manufacturer_monthly_recap import (
    StlBusinessLicenseGblMfrMonthlyRecap,
)
from payit_remittance_report.custom_integration.stl_business_license_hotel_motel_monthly_recap import (
    StlBusinessLicenseHotelMotelMonthlyRecap,
)
from payit_remittance_report.custom_integration.stl_business_license_hotel_payment_monthly_recap import (
    StlBusinessLicenseHotelPaymentMonthlyRecap,
)
from payit_remittance_report.custom_integration.stl_business_license_import import STLBusinessLicenseImport
from payit_remittance_report.custom_integration.stl_business_license_initial_monthly_recap import (
    StlBusinessLicenseInitialMonthlyRecap,
)
from payit_remittance_report.custom_integration.stl_business_license_initial_remittance import STLBusinessLicenseInitial
from payit_remittance_report.custom_integration.stl_business_license_mfg_payment_monthly_recap import (
    StlBusinessLicenseMfgPaymentMonthlyRecap,
)
from payit_remittance_report.custom_integration.stl_business_license_remittance import StlBusinessLicense
from payit_remittance_report.custom_integration.stl_business_license_renewal_applications import (
    STLBusinessLicenseRenewalApplications,
)
from payit_remittance_report.custom_integration.stl_business_license_renewal_apps_monthly_recap import (
    StlBusinessLicenseRenewalAppsMonthlyRecap,
)
from payit_remittance_report.custom_integration.stl_business_license_renewals_monthly_recap import (
    StlBusinessLicenseRenewalsMonthlyRecap,
)
from payit_remittance_report.custom_integration.stl_business_pos_monthly_recap import StlBusinessPosMonthlyRecap
from payit_remittance_report.custom_integration.stl_earnings_tax_custom import STLEarningsTaxCustom
from payit_remittance_report.custom_integration.stl_property_import import STLPropertyImport
from payit_remittance_report.custom_integration.stl_real_estate_import import STLRealEstateImport
from payit_remittance_report.custom_integration.stl_remittance import STLRemittance
from payit_remittance_report.custom_integration.stl_rsi_earnings_tax_payments_import import STLRSIPaymentImport
from payit_remittance_report.custom_integration.stl_suit_import import STLSuitImport
from payit_remittance_report.custom_integration.stl_water_autopay_enrolled import StlWaterAutopayEnrolled
from payit_remittance_report.custom_integration.stl_water_import import STLWaterImport
from payit_remittance_report.custom_integration.stl_water_remittance import STLWaterRemittance
from payit_remittance_report.custom_integration.toronto_daily_deposit_record import TorontoDailyDepositRecord
from payit_remittance_report.custom_integration.toronto_fees_monthly_recap import TorontoFeesMonthlyRecap
from payit_remittance_report.custom_integration.toronto_fees_remittance import TorontoFeesRemittance
from payit_remittance_report.custom_integration.toronto_parking_violations_remittance import (
    TorontoParkingViolationsRemittance,
)
from payit_remittance_report.custom_integration.toronto_property_tax_monthly_enrollments import (
    TorontoPropertyTaxMonthlyEnrollments,
)
from payit_remittance_report.custom_integration.toronto_property_tax_remittance import TorontoPropertyTaxRemittance
from payit_remittance_report.custom_integration.toronto_reconciliation import TorontoReconciliation
from payit_remittance_report.custom_integration.toronto_transactions_monthly_recap import (
    TorontoTransactionsMonthlyRecap,
)
from payit_remittance_report.custom_integration.toronto_utilities_monthly_enrollments import (
    TorontoUtilitiesMonthlyEnrollments,
)
from payit_remittance_report.custom_integration.toronto_utilities_remittance import TorontoUtilitiesRemittance
from payit_remittance_report.custom_integration.va_certification_request_import_file import (
    VACertificationRequestImport,
    VACertificationRequestImportReadable,
)
from payit_remittance_report.custom_integration.va_remittance import VARemittance
from payit_remittance_report.custom_integration.wheeling_court_payments_monthly_recap import (
    WheelingCourtPaymentsMonthlyRecap,
)
from payit_remittance_report.custom_integration.wrangell_ak_property_tax_import import WrangellPtaxImport
from payit_remittance_report.custom_integration.wyco_monthly_recap import WyCoMonthlyRecap
from payit_remittance_report.remittance_report import RemittanceReport
from payit_remittance_report.tracing import instrument

logger: FineReportsLogger = get_logger(__name__)

tracer: Tracer = trace.get_tracer(__name__)


CUSTOM_INTEGRATION_MAP = {
    "aldine_tx_isd_property_tax_financial_remittance": AldinePropertyTaxRemittanceExcel,
    "anoka_child_care_services_monthly_recap": AnokaChildCareServicesMonthlyRecap,
    "anoka_corrections_fees_monthly_recap": AnokaCorrectionsFeesMonthlyRecap,
    "anoka_county_property_tax_financial_remittance": AnokaPropertyTaxRemittanceExcel,
    "anoka_county_property_tax_pos_financial_remittance": AnokaPropertyTaxRemittanceExcel,
    "anoka_county_vital_records_forms_remittance": AnokaCountyVitalRecordsRemittanceExcel,
    "anoka_economic_assistance_overpayments_monthly_recap": AnokaEconomicAssistanceOverpaymentsMonthlyRecap,
    "anoka_highway_permits_monthly_recap": AnokaHighwayPermitsMonthlyRecap,
    "anoka_medical_examiner_records_monthly_recap": AnokaMedicalExaminerRecordsMonthlyRecap,
    "anoka_miscellaneous_payment_monthly_recap": AnokaMiscellaneousPaymentMonthlyRecap,
    "arkansas_outdoors_sdk_financial_remittance": ArkansasOutdoorsRemittance,
    "arkansas_outdoors_sdk_return_financial_remittance": ArkansasOutdoorsRemittance,
    "beaufort_enrollment_monthly_recap": BeaufortEnrollmentMonthlyRecap,
    "beaufort_enrollment_monthly_recap_v2": BeaufortEnrollmentV2MonthlyRecap,
    "beaufort_import_tax_financial_remittance": BeaufortImportTaxRemittance,
    "beaufort_property_tax_ebilling_email_sends_report": PayItEbillingEmailSends,
    "beaufort_ptax_import": BeaufortPtaxImport,
    "beaufort_ptax_installment_remittance": BeaufortPtaxInstallmentRemittance,
    "beaufort_ptax_remittance": BeaufortPtaxRemittance,
    "beaufort_tax_monthly_v2_recap": BeaufortTaxMonthlyV2Recap,
    "buffalo_ny_adjudication_import": BuffaloNYAdjudicationImport,
    "buffalo_ny_dog_licenses_import": BuffaloNYDogLicensesImport,
    "buffalo_ny_general_billing_import": BuffaloNYGeneralBillingImport,
    "buffalo_ny_parking_import": BuffaloNYParkingImport,
    "buffalo_ny_parking_unlisted_forms_import": BuffaloNYParkingUnlistedImport,
    "buffalo_ny_property_tax_import": BuffaloNYPtaxImport,
    "buffalo_ny_user_fees_import": BuffaloNYUserFeesImport,
    "buncombe_nc_property_tax_monthly_recap": BuncombeNcPropertyTaxMonthlyRecap,
    "charleston_aviation_express_monthly_recap": CharlestonAviationExpressMonthlyRecap,
    "co_dl_cash_receipts_remittance": CODLCashReceiptsFinancialRemittance,
    "co_dl_fee_disbursement_monthly_recap": CODLFeeDisbursementFinancialRemittance,
    "co_dl_payment_details_remittance": CODLPaymentDetailsFinancialRemittance,
    "co_dl_receivables_remittance": CODLReceivablesFinancialRemittance,
    "co_dmv_financial_remittance": CODMVFinancialRemittance,
    "columbia_mo_citations_monthly_recap": ColumbiaMOCitationsMonthlyRecap,
    "columbia_mo_gen_pay_home_energy_import": ColumbiaMOGenPayHomeEnergyImport,
    "columbia_mo_gen_pay_import": ColumbiaMOGenPayImport,
    "columbia_mo_general_billing_monthly_recap": ColumbiaMoGeneralBillingMonthlyRecap,
    "columbia_mo_home_energy_loans_monthly_recap": ColumbiaMoHomeEnergyLoansMonthlyRecap,
    "columbia_mo_utilities_billing": ColumbiaMoUtilitiesBilling,
    "columbia_mo_utilities_monthly_recap": ColumbiaMoUtilitiesMonthlyRecap,
    "columbia_mo_utilities_refunds_import": ColumbiaMoUtilitiesImport,
    "columbia_mo_utilities_request_forms_import": ColumbiaMoUtilitiesRequestFormsImport,
    "columbia_mo_utilities_returns_import": ColumbiaMoUtilitiesImport,
    "des_moines_wa_marina_general_billing_import": DesMoinesWAImport,
    "douglas_financial_remittance": DouglasTaxRemittance,
    "douglas_financial_remittance_v2": DouglasTaxRemittance,
    "douglas_import_remittance": DouglasTaxImport,
    "douglas_import_remittance_v2": DouglasTaxImport,
    "essex_on_property_tax_daily_deposit_record": TorontoDailyDepositRecord,
    "essex_on_property_tax_import": EssexOntarioPtaxImport,
    "essex_on_utilities_daily_deposit_record": TorontoDailyDepositRecord,
    "essex_on_utilities_import": EssexOntarioUtilitiesImport,
    "fl_sunpass_daily_import": FLSunPassDailyImport,
    "fl_sunpass_financial_remittance": FLSunPassFinancialRemittance,
    "florida_dmv_financial_remittance": FLDMVRemittance,
    "florida_dmv_monthly_email_campaign": FloridaDMVMonthlyCampaign,
    "florida_dmv_remittance": FLDMVRemittance,
    "fort_smith_utilities_monthly_recap": FortSmithUtilitiesMonthlyRecap,
    "fort_smith_utilities_payments_import": FortSmithUtilitiesPaymentsImport,
    "gr_ar_import": GRARImport,
    "gr_ar_import_v2": GRARImport,
    "gr_ar_import_v3": GRARImportV3,
    "gr_cdl_financial_remittance": GRCDLRemittance,
    "gr_mr_import": GRMRImport,
    "gr_mr_import_v2": GRMRImport,
    "gr_parking_financial_remittance": GRParkingRemittance,
    "gr_parking_import": GRParkingImport,
    "gr_property_tax_end_of_month_midday_financial_remittance": GRPropertyTaxEndOfMonthMiddayRemittance,
    "gr_property_tax_end_of_month_midnight_financial_remittance": GRPropertyTaxEndOfMonthMidnightRemittance,
    "gr_property_tax_financial_remittance": GRPropertyTaxRemittance,
    "gr_refuse_financial_remittance": GRRefuseRemittance,
    "gr_refuse_import": GRRefuseImport,
    "gr_water_financial_remittance": GRWaterRemittance,
    "gr_water_import": GRWaterImport,
    "gr_water_import_v2": GRWaterImportV2,
    "guilford_property_tax_financial_remittance": GuilfordPTaxRemittance,
    "guilford_property_tax_monthly_recap": GuilfordPropertyTaxMonthlyRecap,
    "harvey_property_tax_financial_remittance": KansasCICIntPropertyTaxRemittance,
    "jackson_property_tax_remittance": JacksonPropertyTax,
    "jackson_property_tax_remittance_v2": JacksonPropertyTax,
    "kansas_dl_financial_remittance": KSDLRemittance,
    "kck_tax_financial_remittance": KCKTaxRemittance,
    "kck_tax_financial_remittance_v2": KCKTaxRemittance,
    "kdhe_childcare_financial_remittance": KDHECCLRemittance,
    "kdhe_childcare_financial_remittance_v2": KDHECCLRemittance,
    "kent_county_pos_monthly_recap": KentCountyPosMonthlyRecap,
    "kent_muni_pay_monthly_recap": KentCountyMuniPayMonthlyRecap,
    "kent_pos_courts_financial_remittance": KentCountyPosRemittance,
    "kent_property_tax_monthly_recap": KentCountyPropertyTaxMonthlyRecap,
    "kent_roads_commission_monthly_recap": KentCountyRoadsCommissionMonthlyRecap,
    "knox_county_court_pos_financial_remittance": KnoxCountyCourtPosFinancialRemittance,
    "knox_county_criminal_court_financial_remittance": KnoxCountyCriminalCourtFinancialRemittance,
    "knox_county_criminal_court_import": KnoxCountyCriminalCourtImport,
    "knox_county_criminal_express_financial_remittance": KnoxCountyCriminalExpressFinancialRemittance,
    "knox_county_criminal_sessions_financial_remittance": KnoxCountyCriminalSessionsFinancialRemittance,
    "knox_county_criminal_sessions_import": KnoxCountyCriminalSessionsImport,
    "knox_county_fourth_circuit_express_financial_remittance": KnoxCountyFourthCircuitExpressFinancialRemittance,
    "knox_county_fourth_circuit_financial_remittance": KnoxCountyFourthCircuitFinancialRemittance,
    "knox_county_fourth_circuit_import": KnoxCountyFourthCircuitImport,
    "knox_county_general_session_express_financial_remittance": KnoxCountyGeneralSessionExpressFinancialRemittance,
    "ks_cdl_financial_remittance": KSCDLRemittance,
    "ks_dl_coa_financial_remittance": KSDLCoARemittance,
    "ks_dl_financial_remittance": KSDLRemittance,
    "lansing_mi_building_safety_monthly_recap": LansingMiBuildingSafetyMonthlyRecap,
    "lansing_mi_building_safety_pos_monthly_recap": LansingMiBuildingSafetyPOSMonthlyRecap,
    "lansing_mi_city_attorney_monthly_recap": LansingMiCityAttorneyMonthlyRecap,
    "lansing_mi_clerks_office_monthly_recap": LansingMiClerksOfficeMonthlyRecap,
    "lansing_mi_clerks_office_pos_monthly_recap": LansingMiClerksOfficePOSMonthlyRecap,
    "lansing_mi_code_compliance_monthly_recap": LansingMiCodeComplianceMonthlyRecap,
    "lansing_mi_code_compliance_pos_monthly_recap": LansingMiCodeCompliancePOSMonthlyRecap,
    "lansing_mi_fire_dept_import": LansingMiFireDeptImport,
    "lansing_mi_fire_dept_integrated_monthly_recap": LansingMiFireDeptIntegratedMonthlyRecap,
    "lansing_mi_fire_dept_monthly_recap": LansingMiFireDeptMonthlyRecap,
    "lansing_mi_fire_dept_pos_monthly_recap": LansingMiFireDeptPOSMonthlyRecap,
    "lansing_mi_income_tax_monthly_recap": LansingMiIncomeTaxMonthlyRecap,
    "lansing_mi_miscellaneous_payables_monthly_recap": LansingMiMiscellaneousPayablesMonthlyRecap,
    "lansing_mi_police_dept_monthly_recap": LansingMiPoliceDeptMonthlyRecap,
    "lansing_mi_police_dept_pos_monthly_recap": LansingMiPoliceDeptPOSMonthlyRecap,
    "lansing_mi_property_tax_import": LansingMIPropertyTaxImport,
    "lansing_mi_property_tax_monthly_recap": LansingPropertyTaxMonthlyRecap,
    "lansing_mi_public_service_monthly_recap": LansingMiPublicServiceMonthlyRecap,
    "lansing_mi_public_service_pos_monthly_recap": LansingMiPublicServicePOSMonthlyRecap,
    "lansing_mi_refuse_forms_import": LansingMIRefuseFormsImport,
    "lansing_mi_refuse_import": LansingMIRefuseImport,
    "lansing_mi_treasury_pos_monthly_recap": LansingMiTreasuryPOSMonthlyRecap,
    "lee_county_nc_property_tax_import": LeeCountyNCPtaxImport,
    "memphis_property_tax_import_remittance": MemphisTaxImport,
    "memphis_property_tax_import_remittance_v2": MemphisTaxImport,
    "missouri_outdoors_sdk_financial_remittance": MissouriOutdoorsRemittance,
    "mycabco_property_tax_monthly_recap": MyCabCoPropertyTaxMonthlyRecap,
    "nc_commercial_vehicle_citation_financial_remittance": NCCommercialVehicleCitationRemittance,
    "nc_commercial_vehicle_citation_monthly_recap": NCCommercialVehicleCitationMonthlyRecap,
    "nc_commercial_vehicle_citation_return_financial_remittance": NCCommercialVehicleCitationRemittance,
    "nc_dl_financial_remittance": NCDLRemittance,
    "nc_dl_return_financial_remittance": NCDLRemittance,
    "nc_dl_return_monthly_recap": NCDLReturnMonthlyRecap,
    "nc_ns_express_financial_remittance": NCNSRemittance,
    "nc_ns_express_monthly_recap": NcNsExpressMonthlyRecap,
    "ncdmv_crash_reports_financial_remittance": NCDMVCrashReportsRemittance,
    "ncdmv_crash_reports_monthly_recap": NCDMVCrashReportsMonthlyRecap,
    "ncdmv_crash_reports_return_financial_remittance": NCDMVCrashReportsRemittance,
    "ncdmv_civil_penalty_license_and_theft_financial_remittance": NCDMVHfAhsRemittance,
    "ncdmv_civil_penalty_license_and_theft_monthly_recap": NCDMVCpAhsMonthlyRecap,
    "ncdmv_civil_penalty_license_and_theft_return_financial_remittance": NCDMVHfAhsRemittance,
    "ncdmv_driveway_permits_financial_remittance": NCDMVDrivewayPermitsRemittance,
    "ncdmv_driveway_permits_monthly_recap": NCDMVDrivewayPermitsMonthlyRecap,
    "ncdmv_driveway_permits_return_financial_remittance": NCDMVDrivewayPermitsRemittance,
    "ncdmv_driving_record_financial_remittance": NCDMVDrivingRecordRemittance,
    "ncdmv_driving_record_monthly_recap": NCDMVDrivingRecordMonthlyRecap,
    "ncdmv_driving_record_return_financial_remittance": NCDMVDrivingRecordRemittance,
    "ncdmv_financial_remittance": NCDMVRemittance,
    "ncdmv_handicap_placard_financial_remittance": NCDMVHandicapPlacardRemittance,
    "ncdmv_handicap_placard_monthly_recap": NCDMVHandicapPlacardMonthlyRecap,
    "ncdmv_handicap_placard_return_financial_remittance": NCDMVHandicapPlacardRemittance,
    "ncdmv_hearing_fee_financial_remittance": NCDMVHearingFeeRemittance,
    "ncdmv_hearing_fee_license_and_theft_financial_remittance": NCDMVHfAhsRemittance,
    "ncdmv_hearing_fee_license_and_theft_monthly_recap": NCDMVHfAhsMonthlyRecap,
    "ncdmv_hearing_fee_license_and_theft_return_financial_remittance": NCDMVHfAhsRemittance,
    "ncdmv_hearing_fee_lite_financial_remittance": NCDMVHFLitesRemittance,
    "ncdmv_hearing_fee_lite_monthly_recap": NCDMVHFLitesMonthlyRecap,
    "ncdmv_hearing_fee_lite_return_financial_remittance": NCDMVHFLitesRemittance,
    "ncdmv_hearing_fee_monthly_recap": NCDMVHearingFeeMonthlyRecap,
    "ncdmv_hearing_fee_return_financial_remittance": NCDMVHearingFeeRemittance,
    "ncdmv_lapse_payment_financial_remittance": NCDMVLapsePaymentRemittance,
    "ncdmv_lapse_payment_monthly_recap": NCDMVLapsePaymentMonthlyRecap,
    "ncdmv_lapse_payment_return_financial_remittance": NCDMVLapsePaymentRemittance,
    "ncdmv_persona_identity_verification": NCDMVPersonaIdentityVerificationRemittance,
    "ncdmv_return_financial_remittance": NCDMVRemittance,
    "ncdmv_return_monthly_recap": NCDMVReturnMonthlyRecap,
    "ncdmv_stars_919_financial_remittance": NCDMVRemittance,
    "ncdmv_stars_919_return_financial_remittance": NCDMVRemittance,
    "nj_isf_tolls_financial_remittance": NJISFRemittance,
    "nj_isf_tolls_payments_import": NJISF,
    "olive_branch_courts_client_excel": OliveBranchCourtsExcel,
    "olive_branch_courts_express_client_excel": OliveBranchCourtsExpressExcel,
    "olive_branch_courts_express_monthly_recap": OliveBranchCourtsExpressMonthlyRecap,
    "olive_branch_courts_monthly_recap": OliveBranchCourtsMonthlyRecap,
    "olive_branch_courts_payments_import": OliveBranchCourtsPaymentsImport,
    "olive_branch_courts_pos_client_excel": OliveBranchCourtsPosExcel,
    "olive_branch_courts_pos_monthly_recap": OliveBranchCourtsPosMonthlyRecap,
    "olive_branch_utilities_express_monthly_recap": OliveBranchUtilitiesExpressMonthlyRecap,
    "olive_branch_utilities_monthly_recap": OliveBranchUtilitiesMonthlyRecap,
    "olive_branch_utilities_payments_import": OliveBranchUtilitiesPaymentsImport,
    "olive_branch_utilities_pos_monthly_recap": OliveBranchUtilitiesPosMonthlyRecap,
    "one_alabama_tanf_monthly": OneAlabamaTanfMonthly,
    "orange_county_property_tax_ebilling_email_sends_report": PayItEbillingEmailSends,
    "orange_county_ptax_financial_remittance": OCNCPTaxRemittance,
    "orange_county_ptax_monthly_financial_remittance": OCNCMonthlyRemittance,
    "palm_beach_county_alacarte_monthly_recap": PalmBeachCountyAlacarteMonthlyRecap,
    "palm_beach_county_courts_enrollments_weekly_recap": PalmBeachCountyCourtsEnrollmentWeeklyRecap,
    "palm_beach_county_courts_monthly_recap": PalmBeachCountyCourtsMonthlyRecap,
    "palm_beach_county_criminal_monthly_recap": PalmBeachCountyCriminalMonthlyRecap,
    "palm_beach_county_marriage_license_monthly_recap": PalmBeachCountyMarriageLicenseMonthlyRecap,
    "palm_beach_county_mediation_monthly_recap": PalmBeachCountyMediationMonthlyRecap,
    "palm_beach_county_misc_monthly_recap": PalmBeachCountyMiscMonthlyRecap,
    "palm_beach_county_selfservice_monthly_recap": PalmBeachCountySelfServiceMonthlyRecap,
    "payit_marketing_ncdmv_vehicle_expirations_import": PayItMarketingNCDMVVehicleExpirationsImport,
    "pitt_county_nc_property_tax_import": PittCountyNCPropertyTaxImport,
    "pitt_county_nc_register_deeds_pos_monthly_recap": PittCountyRegisterOfDeedsMonthlyRecap,
    "pitt_nc_solid_waste_import": PittNCSoldWasteImport,
    "pueblo_co_property_tax_payments_import": PuebloCoPropertyTaxPaymentsImport,
    "shawnee_property_tax_financial_remittance": KansasCICIntPropertyTaxRemittance,
    "shawnee_property_tax_monthly_recap": ShawneePropertyTaxMonthlyRecap,
    "shelby_tax_financial_remittance": ShelbyRemittance,
    "shelby_tax_financial_remittance_v2": ShelbyRemittanceV2,
    "shelby_tax_financial_remittance_v3": ShelbyRemittance,  # _v3 is a new version of v1 that uses a JSON config file,
    "shelby_tax_monthly_billing": ShelbyTaxMonthlyBilling,
    "smith_property_tax_import": SmithPropertyTaxImport,
    "southaven_ms_utilities_payments_import": SouthavenUtilitiesPaymentsImport,
    "stl_business_license_application_payment_financial_remittance": StlBusinessLicense,
    "stl_business_license_application_payment_monthly_recap": StlBusinessLicenseApplicationPaymentMonthlyRecap,
    "stl_business_license_financial_remittance": StlBusinessLicense,
    "stl_business_license_gbl_manufacturer_financial_remittance": StlBusinessLicense,
    "stl_business_license_gbl_manufacturer_monthly_recap": StlBusinessLicenseGblMfrMonthlyRecap,
    "stl_business_license_hotel_motel_financial_remittance": StlBusinessLicense,
    "stl_business_license_hotel_motel_monthly_recap": StlBusinessLicenseHotelMotelMonthlyRecap,
    "stl_business_license_hotels_payment_financial_remittance": StlBusinessLicense,
    "stl_business_license_hotels_payment_monthly_recap": StlBusinessLicenseHotelPaymentMonthlyRecap,
    "stl_business_license_import": STLBusinessLicenseImport,
    "stl_business_license_initial_monthly_recap": StlBusinessLicenseInitialMonthlyRecap,
    "stl_business_license_initial_remittance": STLBusinessLicenseInitial,
    "stl_business_license_manufacturing_payment_financial_remittance": StlBusinessLicense,
    "stl_business_license_mfg_payment_monthly_recap": StlBusinessLicenseMfgPaymentMonthlyRecap,
    "stl_business_license_renewal_applications": STLBusinessLicenseRenewalApplications,
    "stl_business_license_renewal_apps_monthly_recap": StlBusinessLicenseRenewalAppsMonthlyRecap,
    "stl_business_license_renewals_monthly_recap": StlBusinessLicenseRenewalsMonthlyRecap,
    "stl_business_pos_monthly_recap": StlBusinessPosMonthlyRecap,
    "stl_earnings_tax_custom": STLEarningsTaxCustom,
    "stl_property_financial_remittance": STLRemittance,
    "stl_property_financial_remittance_v2": STLRemittance,
    "stl_property_import_remittance": STLPropertyImport,
    "stl_property_import_remittance_v2": STLPropertyImport,
    "stl_real_estate_financial_remittance": STLRemittance,
    "stl_real_estate_financial_remittance_v2": STLRemittance,
    "stl_real_estate_import_remittance": STLRealEstateImport,
    "stl_real_estate_import_remittance_v2": STLRealEstateImport,
    "stl_rsi_earnings_tax_payments_import": STLRSIPaymentImport,
    "stl_suit_import_remittance": STLSuitImport,
    "stl_suit_import_remittance_v2": STLSuitImport,
    "stl_water_autopay_enrolled": StlWaterAutopayEnrolled,
    "stl_water_financial_remittance": STLRemittance,
    "stl_water_financial_remittance_v2": STLWaterRemittance,
    "stl_water_import_remittance": STLWaterImport,
    "stouffville_daily_deposit_record": TorontoDailyDepositRecord,
    "toronto_parking_violations_daily_deposit_record": TorontoDailyDepositRecord,
    "toronto_parking_violations_fees_monthly_recap": TorontoFeesMonthlyRecap,
    "toronto_parking_violations_fees_remittance": TorontoFeesRemittance,
    "toronto_parking_violations_financial_remittance": TorontoParkingViolationsRemittance,
    "toronto_parking_violations_monthly_recap": TorontoTransactionsMonthlyRecap,
    "toronto_property_tax_daily_deposit_record": TorontoDailyDepositRecord,
    "toronto_property_tax_fees_monthly_recap": TorontoFeesMonthlyRecap,
    "toronto_property_tax_fees_remittance": TorontoFeesRemittance,
    "toronto_property_tax_financial_remittance": TorontoPropertyTaxRemittance,
    "toronto_property_tax_monthly_enrollments": TorontoPropertyTaxMonthlyEnrollments,
    "toronto_property_tax_monthly_recap": TorontoTransactionsMonthlyRecap,
    "toronto_reconciliation_not_settled": TorontoReconciliation,
    "toronto_reconciliation_parking_violations_not_settled": TorontoReconciliation,
    "toronto_reconciliation_parking_violations_settled": TorontoReconciliation,
    "toronto_reconciliation_settled": TorontoReconciliation,
    "toronto_utilities_daily_deposit_record": TorontoDailyDepositRecord,
    "toronto_utilities_fees_monthly_recap": TorontoFeesMonthlyRecap,
    "toronto_utilities_fees_remittance": TorontoFeesRemittance,
    "toronto_utilities_financial_remittance": TorontoUtilitiesRemittance,
    "toronto_utilities_monthly_enrollments": TorontoUtilitiesMonthlyEnrollments,
    "toronto_utilities_monthly_recap": TorontoTransactionsMonthlyRecap,
    "virginia_licensing_financial_remittance": VARemittance,
    "virginia_licensing_import_remittance": VACertificationRequestImport,
    "virginia_licensing_readable_import_remittance": VACertificationRequestImportReadable,
    "wheeling_court_payments_monthly_recap": WheelingCourtPaymentsMonthlyRecap,
    "wrangell_ak_property_tax_import": WrangellPtaxImport,
    "wyco_monthly_recap": WyCoMonthlyRecap,
}


@instrument
class RemittanceReportWrapper(object):
    def __init__(self):
        start = time()
        r = RemittanceReport()
        report_name = r._report_name
        report_list: list[str] = [report_name]

        for report in report_list:
            logger.info(f"Processing Report: {report}")
            # _replace_variable_value_in_args('report_name', report)
            r_report = RemittanceReport(report_name=report)
            if report in CUSTOM_INTEGRATION_MAP:
                r_report = CUSTOM_INTEGRATION_MAP[report]()

            try:
                r_report.run_report_over_date_range()

            except Exception as e:
                logger.opt(exception=e).error(f"Exception: {e}")
                if report_name != "ALL":
                    raise e

        delta = time() - start
        logger.info(f"Report [{report_name}] time took {delta} seconds")
