tasks:
  default:
    cmds:
      - task --list
    silent: true
  cli:
    cmds:
      - poetry run python -m fine_reports.cli {{.CLI_ARGS}}
    desc: Run a fine_reports CLI command
  contrib-google-groups:
    cmds:
      - poetry run python scripts/contrib/google_groups/main.py
    desc: Run the Google Groups Creator application
    dotenv:
      - .env-google
  coverage:
    deps:
      - coverage-tests
      - coverage-xml
    desc: Run tests with coverage and generate coverage report
  coverage-combine:
    cmds:
      - poetry run coverage combine .coverage-unit .coverage-integration
    desc: Combine the unit test and integration test coverage files into a single .coverage file
  coverage-combined-xml:
    cmds:
      - poetry run coverage xml --data-file=.coverage -o .pytest-coverage-combined.xml
    desc: Generate coverage report from the combined coverage files
  coverage-tests:
    cmds:
      - poetry run coverage run -m pytest tests/
    desc: Run tests with coverage
    env:
      COVERAGE_SUFFIX: '-unit'
      ENV_FOR_DYNACONF: test
  coverage-xml:
    cmds:
      - poetry run coverage xml -o .pytest-coverage.xml
    desc: Generate coverage report
    env:
      COVERAGE_SUFFIX: '-unit'
  dagster-dev:
    cmds:
      - poetry run dagster dev --port 3040
    desc: Run the dagster webserver locally (at localhost:3040) with remittance_reports asset definitions loaded.
    dotenv:
      - .env-dev
  docker-build:
    cmds:
      - >-
        docker build --secret id=artifactory_username,env=ARTIFACTORY_READER_USERNAME --secret id=artifactory_password,env=ARTIFACTORY_READER_PASSWORD . -t fine_reports:local {{.CLI_ARGS}}
    desc: Build the docker image locally and tag it with `fine_reports:local`
    env:
      DOCKER_BUILDKIT: '1'
  docker-build-dagster:
    cmds:
      - >-
        docker build --secret id=artifactory_username,env=ARTIFACTORY_READER_USERNAME --secret id=artifactory_password,env=ARTIFACTORY_READER_PASSWORD -f Dockerfile.dagster . -t fine_reports:local-dagster
        {{.CLI_ARGS}}
    desc: Build the Dagster docker image locally and tag it with `fine_reports:local-dagster`
    env:
      DOCKER_BUILDKIT: '1'
  generate-snowflake-migration-report:
    cmds:
      - poetry run python scripts/generate_snowflake_migration_report.py
    desc: Generate CSV summary reports of Snowflake migration status
  install:
    cmds:
      - poetry install --with testing,models,docs --sync
    desc: Reinstall dependencies from the project's lockfile
  integration-coverage:
    deps:
      - coverage-tests
      - coverage-xml
    desc: Run integration tests with coverage and generate coverage report
  integration-coverage-tests:
    cmds:
      - poetry run coverage run -m pytest tests_integration/
    desc: Run integration tests with coverage
    env:
      COVERAGE_SUFFIX: '-integration'
      ENV_FOR_DYNACONF: test
  integration-coverage-xml:
    cmds:
      - poetry run coverage xml -o .pytest-integration-coverage.xml
    desc: Generate integration coverage report
    env:
      COVERAGE_SUFFIX: '-integration'
  integration-tests:
    cmds:
      - poetry run python -m pytest tests_integration/
    desc: Run integration tests
    env:
      ENV_FOR_DYNACONF: test
  lint-check-snowpaw-queries:
    cmds:
      - poetry run sqlfluff lint --ignore-local-config --config .sqlfluff-snowflake fine_reports/queries/snowpaw/
    desc: Run 'sqlfluff lint' on the SnowPAW SQL queries
  lint-fix-snowpaw-queries:
    cmds:
      - poetry run sqlfluff fix --ignore-local-config --config .sqlfluff-snowflake fine_reports/queries/snowpaw/
    desc: Run 'sqlfluff fix' on the SnowPAW SQL queries
  print-settings:
    cmds:
      - poetry run python -c 'import json; from config import settings; print(json.dumps(dict(settings), indent=2))'
    desc: Print remittance_report settings (including credentials, use caution!) for local debugging
  regenerate-yaml-configs:
    cmds:
      - zsh ./regenerate-yaml-configs.sh
    desc: Sync YAML report configuration files to the JSON report configuration files which are the source of truth.
  smoke-test:
    cmds:
      - poetry run python -c 'from entrypoint import smoke_test; smoke_test()'
    desc: Run a smoke-test command that will fail if any imports cannot be resolved
  start-oplog-docker-container:
    cmds:
      - >-
        docker run \
          --rm \
          -d \
          --env POSTGRES_DB=rds_superuser \
          --env POSTGRES_USER=rds_superuser \
          --env POSTGRES_PASSWORD=rds_superuser_test \
          --env LB_PASSWORD=liquibase_test \
          --name oplog_db \
          -p 5432:5432 \
          payitadmin/oplog_db:2.0.1
    desc: Run the oplog_db docker container (in detached mode) for integration tests
    summary: |
      Run the oplog_db docker container for integration tests. This container image
      is defined in https://github.com/payitgov/oplog_db_sql and uses Liquibase. As of
      version 2.0.1 we have migrated off the Liquibase Pro plan for this project. It no
      longer requires a Liquibase Pro key to run the container. You can view the logs of
      the container with the command `docker logs oplog_db` (since the `docker run` command
      gives the container that name).
  tests:
    cmds:
      - poetry run python -m pytest tests/
    desc: Run tests
    env:
      ENV_FOR_DYNACONF: test
  update-report-configs-with-custom-integration-modules:
    cmds:
      - poetry run python scripts/add_custom_integration_module_to_json_report_configuration_files.py
    desc: Update JSON report configuration files with custom_integration_module information from remittance_report_wrapper
  update-report-configs-with-snowflake-dependencies:
    cmds:
      - poetry run python scripts/add_snowpaw_remittance_model_dependencies_to_json_report_configuration_files.py
    desc: Update JSON report configuration files with Snowflake table information from detail_sql_file property
version: '3'
