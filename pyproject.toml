[tool.poetry]
name = "remittance-reports"
version = "12.10.0"
description = "PayIt Reporting"
authors = [
    "PayIt Data Team <<EMAIL>>"
]
readme = "README.md"

packages = [
    { include = "disbursement_report" },
    { include = "fine_reports" },
    { include = "gr_water_ebilling_reports" },
    { include = "payit_311_remittance" },
    { include = "payit_remittance_report" },
    { include = "remittance" },
    { include = "report_commons" },
    { include = "util" },
]

[tool.poetry.dependencies]
python = ">=3.12,<3.13"
pygresql = "^5.2.4"
awscli = "^1.27.151"
boto3 = "^1.26.151"
cryptography = "^44.0.1"
fpdf = "^1.7.2"
fsspec = "^2023.6.0"
jsonschema = "^4.17.3"
pandas = "^2.2.3"
pyathena = "^3.0.3"
pymongo = "4.6.3"
s3fs = "^0.4.2"
sentry-sdk = "^2.17.0"
xlrd = "^2.0.1"
xlsxwriter = "^3.1.2"
dynaconf = "^3.2.6"
numpy = "^2.1.2"
loguru = "^0.7.0"
pdpyras = "^5.0.3"
kafka-python = "^2.0.2"
opentelemetry-distro = "==0.40b0"
opentelemetry-exporter-otlp = "^1.17.0"
opentelemetry-instrumentation-aws-lambda = "==0.40b0"
opentelemetry-instrumentation-dbapi = "==0.40b0"
opentelemetry-instrumentation-logging = "==0.40b0"
opentelemetry-instrumentation-sqlite3 = "==0.40b0"
opentelemetry-instrumentation-urllib = "==0.40b0"
opentelemetry-instrumentation-wsgi = "==0.40b0"
opentelemetry-instrumentation-asgi = "==0.40b0"
opentelemetry-instrumentation-boto3sqs = "==0.40b0"
opentelemetry-instrumentation-botocore = "==0.40b0"
opentelemetry-instrumentation-grpc = "==0.40b0"
opentelemetry-instrumentation-jinja2 = "==0.40b0"
opentelemetry-instrumentation-kafka-python = "==0.40b0"
opentelemetry-instrumentation-pymongo = "==0.40b0"
opentelemetry-instrumentation-requests = "==0.40b0"
opentelemetry-instrumentation-sqlalchemy = "==0.40b0"
opentelemetry-instrumentation-system-metrics = "==0.40b0"
opentelemetry-instrumentation-tortoiseorm = "==0.40b0"
opentelemetry-instrumentation-urllib3 = "==0.40b0"
opentelemetry-sdk = "==1.19.0"
honeycomb-opentelemetry = "^0.2.2b0"
opentelemetry-api = "==1.19.0"
pydantic = "^2.11.1"
PyYAML =  "^6.0.1"
typing-extensions = "^4.6.3"
crontab = "^1.0.1"
SQLAlchemy = "^2.0.31"
pg8000 = "^1.29.8"
setuptools = "^75.6.0"
certifi = "^2024.8.30"
openpyxl = "^3.1.2"
grpcio = "^1.57.0"
payit-data-common = {version = "^1.4.0", source = "payit_artifactory"}
rich = "^13.7.1"
requests = "^2.32.0"
urllib3 = "^2.2.2"
snowflake-sqlalchemy = "^1.6.1"
jinjasql2 = {git = "https://github.com/pythonutilities/jinjasql.git", rev = "0.1.11"}
typer = "^0.12.5"
tabulate = "^0.9.0"
python-dateutil = "^2.9.0.post0"
snowflake-connector-python = {extras = ["secure-local-storage"], version = "^3.13.2"}
dagster = "^1.9.11"
dagster-docker = "^0.25.11"
dagster-postgres = "^0.25.11"
dagster-slack = "^0.25.11"
dagster-pagerduty = "^0.25.11"
pandera = "^0.23.1"
rapidfuzz = "^3.13.0"
dagster-k8s = "^0.25.11"

[tool.poetry.group.testing]
optional = true

[tool.poetry.group.testing.dependencies]
mock = "^5.0.2"
pytest = "^8.2.2"
pytest-postgresql = "^6.0.0"
coverage = "^7.5.3"
moto = {extras = ["s3"], version = "^5.0.14"}
freezegun = "^1.5.1"
cyclonedx-bom = "^4.4.3"
sqlfluff = "^3.1.1"
sqlglot = {extras = ["rs"], version = "^25.21.2"}
notebook = "^7.2.2"
mypy = {extras = ["reports"], version = "^1.15.0"}
types-pytz = "^2024.2.0.20241003"
types-python-crontab = "^3.2.0.20240703"
types-setuptools = "^75.2.0.20241025"
types-tabulate = "^0.9.0.20240106"
types-jsonschema = "^4.23.0.20240813"
types-mock = "^5.1.0.20240425"
types-fpdf2 = "^2.8.1.20241011"
types-pyyaml = "^6.0.12.20240917"
tach = "^0.29.0"
dagster-webserver = "^1.9.11"
pandas-stubs = "^2.2.3.241126"
PySide6 = "^6.9.0"
google-auth-oauthlib = "^1.2.2"
google-api-python-client = "^2.172.0"


[tool.poetry.group.models]
optional = true

[tool.poetry.group.models.dependencies]
datamodel-code-generator = "^0.25.8"

[tool.poetry.group.docs]
optional = true

[tool.poetry.group.docs.dependencies]
json-schema-for-humans = "^1.0.2"

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[[tool.poetry.source]]
name = "payit_artifactory"
url = "https://payitartifactory.jfrog.io/artifactory/api/pypi/pypi/simple"
priority = "explicit"

[[tool.poetry.source]]
name = "payit_artifactory_publish"
url = "https://payitartifactory.jfrog.io/artifactory/api/pypi/pypi-local"
priority = "supplemental"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
fine-reports = "fine_reports.cli.__main__:main"

[tool.ruff]
line-length = 120
target-version = "py312"

[tool.ruff.lint]
select = [
    "E",
    "F",
    "F401", # unused imports
    "F403", # star imports
    "RUF",
    "I001",
]
ignore = [
    "E712" # https://github.com/charliermarsh/ruff/issues/1852
]
exclude = [
    "tests",
]

[tool.ruff.lint.isort]
force-sort-within-sections = true
known-first-party = [
    "analytics_report",
    "billing_report",
    "disbursement_report",
    "gr_water_ebilling_reports",
    "payit_311_remitance",
    "payit_remittance_consolidator",
    "payit_remittance_report",
    "remittance",
    "report_commons",
    "templates",
    "util",
]
known-third-party = [
    "boto3",
    "csv",
    "dynaconf",
    "hera",
    "loguru",
    "numpy",
    "pandas",
    "pg",
    "pymongo",
    "sentry_sdk",
    "xlsxwriter",
    "yaml",
]

[tool.mypy]
enable_error_code = "explicit-override"
explicit_package_bases = true
warn_unused_configs = true
warn_redundant_casts = true
warn_unused_ignores = true
disallow_subclassing_any = true
disallow_untyped_decorators = true
disallow_any_generics = true

## We're disabling these for now but plan to enable them in the future
disable_error_code = [
    "annotation-unchecked",
    "import-untyped",
]

## We'll need to do some work before enabling these
# disallow_untyped_calls = True
# disallow_incomplete_defs = True
# disallow_untyped_defs = True

[tool.coverage.run]
# Set COVERAGE_SUFFIX=-unit or COVERAGE_SUFFIX=-integration as needed
data_file = ".coverage${COVERAGE_SUFFIX}"
source = [
    "disbursement_report",
    "fine_reports",
    "gr_water_ebilling_reports",
    "payit_311_remittance",
    "payit_remittance_consolidator",
    "payit_remittance_report",
    "remittance",
    "report_commons",
    "util",
]

[tool.pytest.ini_options]
# `-rxXs` means show extra info on xfailed, xpassed, and skipped tests
# see https://docs.pytest.org/en/7.1.x/how-to/skipping.html
addopts = [
    # "-rxXs",
    # "-rA", # show all
    "-ra", # show all except pP (passed, and passed with output)
    "-v",
    "--tb=line",
    # "--capture=no", # uncomment to allow logger output during tests
]
testpaths = [
    "tests",
]

[tool.yamlfix]
comments_min_spaces_from_content = 2
comments_require_starting_space = true
explicit_start = false
indent_mapping = 2
indent_offset = 2
indent_sequence = 4
line_length = 180
none_representation = "null"
preserve_quotes = true
quote_basic_values = false
quote_keys_and_basic_values = false
sequence_style = "block_style"

[tool.dagster]
module_name = "fine_reports.dagster.definitions"
code_location_name = "fine-reports"
