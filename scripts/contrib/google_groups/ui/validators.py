"""Input validation utilities for Google Groups application.

This module provides validation functions for user inputs with fail-fast error handling.
"""

import re


class ValidationError(Exception):
    """Raised when input validation fails."""

    pass


def validate_email(email: str) -> str:
    """Validate email address format.

    Args:
        email (str): Email address to validate

    Returns:
        str: The validated email address (trimmed)

    Raises:
        ValidationError: If email format is invalid
    """
    if not email or not isinstance(email, str):
        raise ValidationError("Email address is required")

    email = email.strip()
    if not email:
        raise ValidationError("Email address cannot be empty")

    # Improved RFC 5322 compliant email regex pattern
    # - Local part: alphanumeric and special chars, cannot start/end with dots
    # - Domain part: alphanumeric and hyphens/dots, cannot start with dot or have consecutive dots
    # - TLD: at least 2 alphabetic characters
    email_pattern = (
        r"^[a-zA-Z0-9][a-zA-Z0-9._%+-]*[a-zA-Z0-9]@[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?"
        r"(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$"
    )

    # Handle single character local part
    if "@" in email:
        local_part, domain_part = email.split("@", 1)
        if len(local_part) == 1 and local_part.isalnum():
            # Allow single alphanumeric character in local part
            single_char_pattern = (
                r"^[a-zA-Z0-9]@[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?"
                r"(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$"
            )
            if re.match(single_char_pattern, email):
                return email

    if not re.match(email_pattern, email):
        raise ValidationError(f"Invalid email format: {email}")

    return email


def validate_group_name(name: str) -> str:
    """Validate group name.

    Args:
        name (str): Group name to validate

    Returns:
        str: The validated group name (trimmed)

    Raises:
        ValidationError: If group name is invalid
    """
    if not name or not isinstance(name, str):
        raise ValidationError("Group name is required")

    name = name.strip()
    if not name:
        raise ValidationError("Group name cannot be empty")

    if len(name) > 100:  # Google Groups limit
        raise ValidationError("Group name cannot exceed 100 characters")

    # Check for potentially problematic characters
    if any(char in name for char in ["<", ">", '"', "\n", "\r", "\t"]):
        raise ValidationError("Group name contains invalid characters")

    return name


def validate_email_prefix(prefix: str) -> str:
    """Validate email prefix (part before @domain).

    Args:
        prefix (str): Email prefix to validate

    Returns:
        str: The validated email prefix (trimmed and lowercase)

    Raises:
        ValidationError: If email prefix is invalid
    """
    if not prefix or not isinstance(prefix, str):
        raise ValidationError("Email prefix is required")

    prefix = prefix.strip().lower()
    if not prefix:
        raise ValidationError("Email prefix cannot be empty")

    # Check for valid characters (alphanumeric, hyphens, dots, underscores)
    if not re.match(r"^[a-z0-9._-]+$", prefix):
        raise ValidationError("Email prefix can only contain letters, numbers, dots, hyphens, and underscores")

    # Cannot start or end with special characters
    if prefix.startswith(".") or prefix.endswith(".") or prefix.startswith("-") or prefix.endswith("-"):
        raise ValidationError("Email prefix cannot start or end with dots or hyphens")

    # Cannot have consecutive dots
    if ".." in prefix:
        raise ValidationError("Email prefix cannot contain consecutive dots")

    if len(prefix) > 64:  # Email local part limit
        raise ValidationError("Email prefix cannot exceed 64 characters")

    return prefix


def validate_description(description: str) -> str:
    """Validate group description.

    Args:
        description (str): Group description to validate

    Returns:
        str: The validated description (trimmed)

    Raises:
        ValidationError: If description is invalid
    """
    if not description or not isinstance(description, str):
        # Description is optional, return empty string
        return ""

    description = description.strip()

    if len(description) > 300:  # Reasonable limit for descriptions
        raise ValidationError("Group description cannot exceed 300 characters")

    # Check for email patterns in the description
    # Use a simplified email regex to detect email-like patterns
    email_pattern = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"

    if re.search(email_pattern, description):
        raise ValidationError(
            "Group description should not contain email addresses. Use plain text to describe the group's purpose."
        )

    return description


def parse_and_validate_emails(email_text: str) -> list[str]:
    """Parse and validate a comma-separated string of emails.

    Args:
        email_text (str): Comma-separated email addresses

    Returns:
        list[str]: List of validated email addresses

    Raises:
        ValidationError: If any email is invalid or if no valid emails are found
    """
    if not email_text or not isinstance(email_text, str):
        return []

    email_text = email_text.strip()
    if not email_text:
        return []

    # Split by comma and validate each email
    raw_emails = [email.strip() for email in email_text.split(",")]
    validated_emails: list[str] = []
    invalid_emails: list[str] = []

    for email in raw_emails:
        if not email:  # Skip empty strings
            continue
        try:
            validated_email = validate_email(email)
            validated_emails.append(validated_email)
        except ValidationError:
            invalid_emails.append(email)

    if invalid_emails:
        raise ValidationError(f"Invalid email addresses found: {', '.join(invalid_emails)}")

    # Remove duplicates while preserving order
    seen = set()
    unique_emails = []
    for email in validated_emails:
        if email not in seen:
            seen.add(email)
            unique_emails.append(email)

    return unique_emails


def validate_environment_selection(
    prod_selected: bool, staging_selected: bool, group_name: str, is_staging_config: bool = False
) -> None:
    """Validate environment selection logic.

    Args:
        prod_selected (bool): Whether production environment is selected
        staging_selected (bool): Whether staging environment is selected
        group_name (str): The group name to validate against environment rules
        is_staging_config (bool): Whether this is validating a staging-specific config

    Raises:
        ValidationError: If environment selection is invalid
    """
    if not prod_selected and not staging_selected:
        raise ValidationError("At least one environment (Prod or Staging) must be selected")

    group_name_lower = group_name.lower()

    # If only staging is selected, group name must contain "staging"
    if staging_selected and not prod_selected:
        if "staging" not in group_name_lower:
            raise ValidationError("Group name must contain 'staging' when only staging environment is selected")

    # If only prod is selected, group name cannot contain "staging"
    if prod_selected and not staging_selected:
        if "staging" in group_name_lower:
            raise ValidationError("Group name cannot contain 'staging' when production environment is selected")

    # If both environments are selected:
    # - For prod config: the original name cannot contain "staging"
    # - For staging config: the generated name can contain "staging"
    if prod_selected and staging_selected:
        if not is_staging_config and "staging" in group_name_lower:
            raise ValidationError("Group name cannot contain 'staging' when production environment is selected")
