"""Main GUI window for Google Groups Creator application.

This module contains the PyQt6-based user interface with consolidated form management.
"""

import logging
from typing import TYPE_CHECKING, Any
import webbrowser

if TYPE_CHECKING:
    from PySide6.QtWidgets import QCheckBox, QLineEdit

try:
    from PySide6.QtCore import Qt
    from PySide6.QtWidgets import (
        Q<PERSON><PERSON>lication,
        QCheckBox,
        QFormLayout,
        QHBox<PERSON>ayout,
        QLabel,
        QLineEdit,
        QMessageBox,
        QPushButton,
        QTextEdit,
        QVBoxLayout,
        QWidget,
    )
except ImportError as e:
    raise ImportError("PySide6 is required. Install with: pip install PySide6") from e

from scripts.contrib.google_groups.core.config import LOG_AREA_MAX_HEIGHT, LOG_AREA_MIN_HEIGHT, WINDOW_MIN_WIDTH
from scripts.contrib.google_groups.core.group_manager import (
    GoogleGroup<PERSON>anager,
    GroupManagerError,
    create_group_request_from_form_data,
)
from scripts.contrib.google_groups.core.models import BatchCreationResult

logger = logging.getLogger(__name__)


class GoogleGroupCreatorWindow(QWidget):
    """Main window for the Google Groups Creator application."""

    def __init__(self) -> None:
        """Initialize the main window."""
        super().__init__()
        self.setWindowTitle("Google Group Creator")
        self.setMinimumWidth(WINDOW_MIN_WIDTH)

        # Form field references for visibility management
        self.form_fields: dict[str, dict[str, QWidget | QLabel]] = {}

        # Pre-declare member input widgets for type checking
        self.members_input: QLineEdit
        self.managers_input: QLineEdit
        self.owners_input: QLineEdit
        self.descriptions_input: QLineEdit
        self.prod_members_input: QLineEdit
        self.staging_members_input: QLineEdit
        self.prod_managers_input: QLineEdit
        self.staging_managers_input: QLineEdit
        self.prod_owners_input: QLineEdit
        self.staging_owners_input: QLineEdit
        self.prod_descriptions_input: QLineEdit
        self.staging_descriptions_input: QLineEdit
        self.same_members_checkbox: QCheckBox
        self.same_managers_checkbox: QCheckBox
        self.same_owners_checkbox: QCheckBox
        self.same_descriptions_checkbox: QCheckBox

        # Initialize UI components
        self._create_layout()
        self._setup_form_fields()
        self._setup_logging_area()
        self._setup_action_buttons()
        self._connect_signals()
        self._update_form_visibility()

        # Initialize group manager
        self.group_manager = GoogleGroupManager(progress_callback=self._log_message)

    def _create_layout(self) -> None:
        """Create the main layout structure."""
        self.main_layout = QVBoxLayout(self)
        self.form_layout = QFormLayout()
        self.form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        self.form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        self.main_layout.addLayout(self.form_layout)

    def _setup_form_fields(self) -> None:
        """Set up all form input fields."""
        # Environment selection
        self.env_prod = QCheckBox("Prod")
        self.env_staging = QCheckBox("Staging")
        env_layout = QHBoxLayout()
        env_layout.setSpacing(10)
        env_layout.setContentsMargins(0, 0, 0, 0)
        env_layout.addWidget(self.env_prod)
        env_layout.addWidget(self.env_staging)
        env_widget = QWidget()
        env_widget.setLayout(env_layout)
        self._add_form_field("Environment:", env_widget, "environment")

        # Group name
        self.name_input = QLineEdit()
        self.name_input.setToolTip("Enter the display name for the Google Group.")
        self._add_form_field("Group Name:", self.name_input, "group_name")

        # Group email with automated checkbox
        email_layout = QHBoxLayout()
        self.email_input = QLineEdit()
        self.email_automated_checkbox = QCheckBox("automated")
        self.email_automated_checkbox.setChecked(True)
        email_layout.addWidget(self.email_input)
        email_layout.addWidget(self.email_automated_checkbox)
        email_widget = QWidget()
        email_widget.setLayout(email_layout)
        self.email_input.setToolTip("The email address prefix for the group.")
        self._add_form_field("Group Email:", email_widget, "group_email")

        # Group description - using the same pattern as member fields
        self._setup_description_fields()

        # Member configuration fields
        self._setup_member_fields()

    def _setup_description_fields(self) -> None:
        """Set up description configuration fields with same/separate options."""
        # Shared description field with "same for both" checkbox
        shared_input = QLineEdit()
        shared_input.setPlaceholderText("Plain text description of the group's purpose")
        shared_input.setToolTip("Description for both groups, or for the selected group. Use plain text only.")

        same_checkbox = QCheckBox("Same for both groups")
        same_checkbox.setChecked(True)

        shared_layout = QHBoxLayout()
        shared_layout.addWidget(shared_input)
        shared_layout.addWidget(same_checkbox)
        shared_widget = QWidget()
        shared_widget.setLayout(shared_layout)

        self._add_form_field("Group Description:", shared_widget, "group_descriptions_shared")

        # Environment-specific description fields
        prod_input = QLineEdit()
        prod_input.setPlaceholderText("Plain text description for the production group")
        prod_input.setToolTip("Description for the Prod group. Use plain text only.")
        self._add_form_field("Prod Group Description:", prod_input, "prod_descriptions")

        staging_input = QLineEdit()
        staging_input.setPlaceholderText("Plain text description for the staging group")
        staging_input.setToolTip("Description for the Staging group. Use plain text only.")
        self._add_form_field("Staging Group Description:", staging_input, "staging_descriptions")

        # Store references for easy access
        self.descriptions_input = shared_input
        self.same_descriptions_checkbox = same_checkbox
        self.prod_descriptions_input = prod_input
        self.staging_descriptions_input = staging_input

    def _setup_member_fields(self) -> None:
        """Set up member, manager, and owner configuration fields."""
        member_configs = [
            ("members", "Group Members:", "<EMAIL>, <EMAIL>"),
            ("managers", "Group Managers:", "<EMAIL>, <EMAIL>"),
            ("owners", "Group Owners:", "<EMAIL>, <EMAIL>"),
        ]

        for member_type, label, placeholder in member_configs:
            # Shared field with "same for both" checkbox
            shared_input = QLineEdit()
            shared_input.setPlaceholderText(placeholder)
            shared_input.setToolTip(
                f"Comma-separated list of {member_type} for both groups, or for the selected group."
            )

            same_checkbox = QCheckBox("Same for both groups")
            same_checkbox.setChecked(True)

            shared_layout = QHBoxLayout()
            shared_layout.addWidget(shared_input)
            shared_layout.addWidget(same_checkbox)
            shared_widget = QWidget()
            shared_widget.setLayout(shared_layout)

            self._add_form_field(label, shared_widget, f"group_{member_type}_shared")

            # Environment-specific fields
            prod_input = QLineEdit()
            prod_input.setPlaceholderText(f"prod_{member_type[:-1]}<EMAIL>, prod_{member_type[:-1]}<EMAIL>")
            prod_input.setToolTip(f"Comma-separated list of Prod {member_type}.")
            self._add_form_field(f"Prod {label}", prod_input, f"prod_{member_type}")

            staging_input = QLineEdit()
            staging_input.setPlaceholderText(
                f"staging_{member_type[:-1]}<EMAIL>, staging_{member_type[:-1]}<EMAIL>"
            )
            staging_input.setToolTip(f"Comma-separated list of Staging {member_type}.")
            self._add_form_field(f"Staging {label}", staging_input, f"staging_{member_type}")

            # Store references for easy access
            setattr(self, f"{member_type}_input", shared_input)
            setattr(self, f"same_{member_type}_checkbox", same_checkbox)
            setattr(self, f"prod_{member_type}_input", prod_input)
            setattr(self, f"staging_{member_type}_input", staging_input)

    def _add_form_field(self, label_text: str, widget: QWidget, field_key: str) -> None:
        """Add a form field to the layout and store references.

        Args:
            label_text (str): Text for the field label
            widget (QWidget): The input widget
            field_key (str): Key for storing field references
        """
        label = QLabel(label_text)
        self.form_layout.addRow(label, widget)
        self.form_fields[field_key] = {"label": label, "widget": widget}

    def _setup_logging_area(self) -> None:
        """Set up the logging/progress area."""
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        self.log_area.setMinimumHeight(LOG_AREA_MIN_HEIGHT)
        self.log_area.setMaximumHeight(LOG_AREA_MAX_HEIGHT)
        self.log_area.setStyleSheet("background-color: #f5f5f5; color: #222; font-size: 13px;")
        self.main_layout.addWidget(self.log_area)

    def _setup_action_buttons(self) -> None:
        """Set up action buttons."""
        # Open URL checkbox
        self.open_url_checkbox = QCheckBox("Open Google Group URL")
        self.open_url_checkbox.setChecked(True)
        url_layout = QHBoxLayout()
        url_layout.addWidget(self.open_url_checkbox)
        url_layout.addStretch(1)
        self.main_layout.addLayout(url_layout)

        # Create button
        self.create_button = QPushButton("Create Group")
        self.main_layout.addWidget(self.create_button, 0, Qt.AlignmentFlag.AlignBottom)

    def _connect_signals(self) -> None:
        """Connect UI signals to their handlers."""
        # Email automation
        self.name_input.textChanged.connect(self._update_email_automation)
        self.email_automated_checkbox.stateChanged.connect(self._toggle_email_automation)

        # Environment and visibility management
        self.env_prod.stateChanged.connect(self._on_environment_changed)
        self.env_staging.stateChanged.connect(self._on_environment_changed)

        # Member field visibility
        for member_type in ["members", "managers", "owners"]:
            checkbox = getattr(self, f"same_{member_type}_checkbox")
            checkbox.stateChanged.connect(self._update_form_visibility)

        # Description field visibility
        self.same_descriptions_checkbox.stateChanged.connect(self._update_form_visibility)

        # Create button
        self.create_button.clicked.connect(self._on_create_group)

    def _update_email_automation(self) -> None:
        """Update the email prefix based on group name if automation is enabled."""
        if self.email_automated_checkbox.isChecked():
            group_name = self.name_input.text().strip()
            auto_email = group_name.lower().replace(" ", "-")
            self.email_input.setText(auto_email)

    def _toggle_email_automation(self) -> None:
        """Toggle email automation on/off."""
        if self.email_automated_checkbox.isChecked():
            self.email_input.setReadOnly(True)
            self._update_email_automation()
        else:
            self.email_input.setReadOnly(False)

    def _on_environment_changed(self) -> None:
        """Handle environment selection changes."""
        # Reset "same for both" checkboxes when environment selection changes
        if self.env_prod.isChecked() or self.env_staging.isChecked():
            for member_type in ["members", "managers", "owners"]:
                checkbox = getattr(self, f"same_{member_type}_checkbox")
                checkbox.setChecked(True)
            # Also reset descriptions checkbox
            self.same_descriptions_checkbox.setChecked(True)

        self._update_form_visibility()

    def _update_form_visibility(self) -> None:
        """Update form field visibility based on current selections."""
        prod_selected = self.env_prod.isChecked()
        staging_selected = self.env_staging.isChecked()
        both_selected = prod_selected and staging_selected

        # Update visibility for each member type and descriptions
        for member_type in ["members", "managers", "owners"]:
            self._update_member_type_visibility(member_type, both_selected)

        self._update_member_type_visibility("descriptions", both_selected)

    def _update_member_type_visibility(self, member_type: str, both_selected: bool) -> None:
        """Update visibility for a specific member type (members, managers, owners).

        Args:
            member_type (str): The member type to update
            both_selected (bool): Whether both environments are selected
        """
        same_checkbox = getattr(self, f"same_{member_type}_checkbox")
        shared_field_key = f"group_{member_type}_shared"
        prod_field_key = f"prod_{member_type}"
        staging_field_key = f"staging_{member_type}"

        # Show/hide "same for both" checkbox
        same_checkbox.setVisible(both_selected)

        if both_selected:
            # Both environments selected - show appropriate fields based on "same" checkbox
            show_shared = same_checkbox.isChecked()
            self._set_field_visibility(shared_field_key, show_shared)
            self._set_field_visibility(prod_field_key, not show_shared)
            self._set_field_visibility(staging_field_key, not show_shared)
        else:
            # Single environment - show shared field, hide environment-specific ones
            self._set_field_visibility(shared_field_key, True)
            self._set_field_visibility(prod_field_key, False)
            self._set_field_visibility(staging_field_key, False)

    def _set_field_visibility(self, field_key: str, visible: bool) -> None:
        """Set visibility of a form field.

        Args:
            field_key (str): Key of the field to show/hide
            visible (bool): Whether the field should be visible
        """
        if field_key in self.form_fields:
            self.form_fields[field_key]["label"].setVisible(visible)
            widget = self.form_fields[field_key]["widget"]
            widget.setVisible(visible)

            # For composite widgets, also show/hide the input component
            if hasattr(widget, "layout"):
                layout = widget.layout()
                if layout is not None and layout.itemAt(0) is not None:
                    item = layout.itemAt(0)
                    if item and item.widget():
                        item.widget().setVisible(visible)

    def _log_message(self, message: str, color: str = "black") -> None:
        """Add a message to the log area.

        Args:
            message (str): Message to log
            color (str): Text color for the message
        """
        html = f'<span style="color:{color}">{message}</span>'
        self.log_area.append(html)
        self.log_area.verticalScrollBar().setValue(self.log_area.verticalScrollBar().maximum())
        QApplication.processEvents()  # Allow UI to update

    def _on_create_group(self) -> None:
        """Handle the create group button click."""
        try:
            self.create_button.setEnabled(False)
            self._log_message("Starting group creation process...", "blue")

            # Gather form data
            form_data = self._gather_form_data()

            # Create request from form data
            request = create_group_request_from_form_data(**form_data)

            # Execute group creation
            result = self.group_manager.create_groups(request)

            # Handle results
            self._handle_creation_results(result)

        except GroupManagerError as e:
            error_msg = f"Group creation failed: {e}"
            self._log_message(error_msg, "red")
            QMessageBox.critical(self, "Error", error_msg)
            logger.error(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error: {e}"
            self._log_message(error_msg, "red")
            QMessageBox.critical(self, "Unexpected Error", error_msg)
            logger.exception("Unexpected error in group creation")
        finally:
            self.create_button.setEnabled(True)

    def _gather_form_data(self) -> dict[str, Any]:
        """Gather all form data into a dictionary.

        Returns:
            dict[str, Any]: Form data for group creation
        """
        return {
            "name": self.name_input.text().strip(),
            "email_prefix": self.email_input.text().strip(),
            "description": self.descriptions_input.text().strip(),
            "prod_selected": self.env_prod.isChecked(),
            "staging_selected": self.env_staging.isChecked(),
            "members_text": self.members_input.text().strip(),
            "managers_text": self.managers_input.text().strip(),
            "owners_text": self.owners_input.text().strip(),
            "prod_members_text": self.prod_members_input.text().strip(),
            "staging_members_text": self.staging_members_input.text().strip(),
            "prod_managers_text": self.prod_managers_input.text().strip(),
            "staging_managers_text": self.staging_managers_input.text().strip(),
            "prod_owners_text": self.prod_owners_input.text().strip(),
            "staging_owners_text": self.staging_owners_input.text().strip(),
            "prod_descriptions_text": self.prod_descriptions_input.text().strip(),
            "staging_descriptions_text": self.staging_descriptions_input.text().strip(),
            "same_members": self.same_members_checkbox.isChecked(),
            "same_managers": self.same_managers_checkbox.isChecked(),
            "same_owners": self.same_owners_checkbox.isChecked(),
            "same_descriptions": self.same_descriptions_checkbox.isChecked(),
        }

    def _handle_creation_results(self, result: BatchCreationResult) -> None:
        """Handle the results of group creation.

        Args:
            result (BatchCreationResult): Results from group creation
        """
        summary_messages: list[str] = []
        urls_to_open: list[str] = []

        for group_result in result.results:
            if group_result.success:
                message = f"✅ {group_result.environment.title()} group created: {group_result.group_email}"
                self._log_message(message, "green")
                summary_messages.append(f"SUCCESS: {message}\nURL: {group_result.group_url}")

                if self.open_url_checkbox.isChecked():
                    urls_to_open.append(group_result.group_url)
            else:
                message = f"❌ {group_result.environment.title()} group failed: {group_result.group_email}"
                self._log_message(message, "red")
                error_details = "; ".join(group_result.errors) if group_result.errors else "Unknown error"
                summary_messages.append(f"FAILED: {message}\nErrors: {error_details}\nURL: {group_result.group_url}")

        # Show summary dialog
        summary_text = "\n\n".join(summary_messages) if summary_messages else "No groups were processed."
        QMessageBox.information(self, "Group Creation Results", summary_text)

        # Open URLs in browser
        for url in urls_to_open:
            try:
                webbrowser.open_new_tab(url)
            except Exception as e:
                self._log_message(f"Failed to open browser for {url}: {e}", "orange")
