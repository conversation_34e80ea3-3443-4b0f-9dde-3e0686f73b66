# Google Groups Creator

A Python application for creating and configuring Google Groups in a Google Workspace domain.

## Features
- **Dual-Environment Creation**: Can create and configure groups for both production and staging environments in a single operation
- **Auto Group Opening**: Automatically opens created groups in a browser window
- **Validation**: Validates user inputs and environment configurations before processing
- **Error Handling**: Provides clear error messages and feedback to the user
- **Progress Logging**: Logs all operations to a dedicated area in the UI

## Architecture

```
scripts/contrib/google_groups/
├── ui/
│   ├── main_window.py          # GUI components + form state management
│   └── validators.py           # Input validation utilities
├── core/
│   ├── group_manager.py        # Business logic orchestration
│   ├── models.py              # Data models and DTOs
│   └── config.py              # Configuration management
├── api/
│   ├── google_client.py       # Google API wrapper
│   └── auth.py                # OAuth handling
└── main.py                    # Application entry point
```

## Requirements

- Python 3.12+
- PySide6 (Qt6 for Python)
- google-auth-oauthlib
- google-api-python-client
- python-dotenv

## Setup

1. **Install Dependencies**:
   ```bash
   task install
   ```

2. **Set Environment Variables**:
   Create a `.env` file in the workspace root with your Google OAuth credentials:
   ```
   GOOGLE_CLIENT_ID=your_oauth_client_id
   GOOGLE_CLIENT_SECRET=your_oauth_client_secret
   ```

3. **Google Cloud Console Setup**:
   - Enable the Admin SDK API and Groups Settings API
   - Create OAuth 2.0 credentials (Desktop Application type)
   - Add your OAuth client ID and secret to the environment variables

## Usage

### Run the Application

```bash
# Navigate to the application directory and run
cd scripts/contrib/google_groups
python main.py

# Or make it executable and run directly
chmod +x main.py
./main.py
```

### Using the GUI

1. **Select Environment**: Choose Prod, Staging, or both
2. **Enter Group Details**: Name, email prefix, and description
3. **Configure Members**: Add members, managers, and owners
4. **Create Groups**: Click "Create Group" to execute

### Environment Rules

- **Production Only**: Group name cannot contain "staging"
- **Staging Only**: Group name must contain "staging"
- **Both Environments**: Prod group uses provided name, staging group gets "-staging" suffix

## Security Features

- **No credential persistence**: OAuth tokens are never saved to disk
- **Minimal API scopes**: Only requests necessary permissions
- **Input validation**: All user inputs are validated before processing
- **Environment validation**: Checks required environment variables on startup


## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify environment variables are set correctly
2. **Permission Errors**: Ensure your Google account has Admin SDK permissions
3. **Import Errors**: Check that all dependencies are installed
4. **Group Already Exists**: This is expected behavior - the app will report and skip existing groups
