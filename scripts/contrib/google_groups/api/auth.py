"""OAuth authentication for Google APIs.

This module handles OAuth 2.0 authentication flow for accessing Google Admin SDK and Groups Settings APIs.
Credentials are never persisted to disk for security reasons.
"""

import logging
import secrets

try:
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
except ImportError as e:
    raise ImportError("google-auth-oauthlib is required. Install with: pip install google-auth-oauthlib") from e

from scripts.contrib.google_groups.core.config import SCOPES, get_oauth_client_config

logger = logging.getLogger(__name__)


class AuthenticationError(Exception):
    """Raised when OAuth authentication fails."""

    pass


class GoogleAuthenticator:
    """Handles Google OAuth 2.0 authentication flow."""

    def __init__(self) -> None:
        """Initialize the authenticator."""
        self.credentials: Credentials | None = None

    def authenticate(self) -> Credentials:
        """Perform OAuth 2.0 authentication flow.

        Returns:
            Credentials: Valid OAuth 2.0 credentials

        Raises:
            AuthenticationError: If authentication fails for any reason
        """
        try:
            client_config = get_oauth_client_config()
        except ValueError as e:
            raise AuthenticationError(f"OAuth configuration error: {e}") from e

        try:
            flow = InstalledAppFlow.from_client_config(client_config, SCOPES)

            # Add state parameter for CSRF protection
            flow.oauth2session.state = secrets.token_urlsafe(32)

            logger.info("Starting OAuth authentication flow...")

            # Run the OAuth flow - this will open a browser window
            credentials = flow.run_local_server(port=0)

            if not credentials or not credentials.valid:
                raise AuthenticationError("Failed to obtain valid credentials from OAuth flow")

            logger.info("Successfully authenticated with Google APIs")
            self.credentials = credentials
            return credentials

        except Exception as e:
            error_msg = f"OAuth authentication failed: {e}"
            logger.error(error_msg)
            raise AuthenticationError("Authentication failed. Please try again.") from e

    def get_credentials(self) -> Credentials:
        """Get current credentials, authenticating if necessary.

        Returns:
            Credentials: Valid OAuth 2.0 credentials

        Raises:
            AuthenticationError: If authentication fails
        """
        if not self.credentials or not self.credentials.valid:
            return self.authenticate()
        return self.credentials
