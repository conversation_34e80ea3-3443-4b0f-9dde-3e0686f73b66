## Issue 1
```scripts/contrib/google_groups/main.py
from pathlib import Path
import sys```

<PERSON><PERSON><PERSON> 3 hours ago
I would strongly prefer that we do no relative imports. Can we remove this sys.path monkeypatching and just use absolute module paths for all imports?

## Issue 2
scripts/contrib/google_groups/core/__init__.py.backup
menzenski 3 hours ago
The .backup in the name suggests this shouldn't have been committed - were you trying to resolve issues with relative imports by adding an explicit package definition?

## Issue 3
scripts/contrib/google_groups/main.py
Comment on lines +24 to +27
```if TYPE_CHECKING:
    from pathlib import Path as PathType
else:
    PathType = type(Path())```
men<PERSON><PERSON> 3 hours ago
What is this doing?


## Issue 4
scripts/contrib/google_groups/main.py
Comment on lines +38 to +42
# Load environment variables from .env file in the workspace root
workspace_root = Path(__file__).parent.parent.parent.parent
env_file = workspace_root / ".env"
if env_file.exists():
    load_dotenv(env_file)
@menzen<PERSON> menzenski 3 hours ago
I would strongly prefer that we not add dotenv to this project. We use go-task / Taskfile heavily in our projects, and go-task includes dotenv support.

I am leery of supporting .env files in general in remittance reports, and we certainly should not use a file named literally .env (too much potential confusion with existing convention in the Data team around .env-dev / .env-staging / .env-prod etc.

Finally, I don't like that the instructions for this script have you cd into the contrib script subdirectory and run it there. Ideally everything is runnable from the repository root directory, without needing to cd anywhere.

Here is how I would like to do this:

Rename the .env file to be .env-google, to make clear that it's specific for this purpose and so that it won't be picked up unintentionally by a different consumer of env files.
Use a Taskfile entry to run the script, instead of the documented "cd into subdirectory, run poetry run python" process. You can set dir in a Taskfile entry to run a script in a subdirectory, and the dotenv setting is relative to that dir. (Although I would recommend keeping the .env-google in the repository root and just having the Taskfile command be poetry run python scripts/contrib/google_groups/main.py, with the subdirectory in the path.
I don't have an issue with you adding new entries to the Taskfile.yaml in the repository root (this isn't used during the application runtime, and contains some test-only functionality today). I would ask you to prefix all Taskfile commands with contrib- or some other prefix that allows them to be grouped in the Taskfile.yaml file (as this YAML file has to be sorted to pass the linter). You could also define a separate Taskfile in the scripts/contrib/ directory and include it in the main one.