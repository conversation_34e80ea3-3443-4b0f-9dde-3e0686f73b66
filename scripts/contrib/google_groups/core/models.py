"""Data models for Google Groups management application.

This module defines the core data structures used throughout the application.
"""

from dataclasses import dataclass


@dataclass
class GroupConfig:
    """Configuration for a Google Group.

    Args:
        name (str): Display name for the group
        email_prefix (str): Email prefix (without @domain)
        description (str): Group description
        environment (str): Environment type ('prod' or 'staging')
    """

    name: str
    email_prefix: str
    description: str
    environment: str

    @property
    def full_email(self) -> str:
        """Get the full email address for this group.

        Returns:
            str: Full email address (prefix@domain)
        """
        from scripts.contrib.google_groups.core.config import DOMAIN

        return f"{self.email_prefix}@{DOMAIN}"

    @property
    def group_url(self) -> str:
        """Get the Google Groups URL for this group.

        Returns:
            str: Google Groups forum URL
        """
        from scripts.contrib.google_groups.core.config import DOMAIN

        return f"https://groups.google.com/a/{DOMAIN}/forum/#!forum/{self.email_prefix}"


@dataclass
class GroupMembers:
    """Member configuration for a Google Group.

    Args:
        members (list[str]): List of member email addresses
        managers (list[str]): List of manager email addresses
        owners (list[str]): List of owner email addresses
    """

    members: list[str]
    managers: list[str]
    owners: list[str]


@dataclass
class GroupCreationRequest:
    """Request for creating Google Group(s).

    Args:
        prod_config (GroupConfig | None): Production group configuration
        staging_config (GroupConfig | None): Staging group configuration
        prod_members (GroupMembers | None): Production group members
        staging_members (GroupMembers | None): Staging group members
    """

    prod_config: GroupConfig | None
    staging_config: GroupConfig | None
    prod_members: GroupMembers | None
    staging_members: GroupMembers | None


@dataclass
class GroupCreationResult:
    """Result of group creation operation.

    Args:
        success (bool): Whether the operation was successful
        group_email (str): Email address of the created/configured group
        group_url (str): URL of the group
        errors (list[str]): List of errors encountered
        environment (str): Environment this result applies to
    """

    success: bool
    group_email: str
    group_url: str
    errors: list[str]
    environment: str


@dataclass
class BatchCreationResult:
    """Result of batch group creation operation.

    Args:
        results (list[GroupCreationResult]): Individual group results
        overall_success (bool): Whether all operations were successful
    """

    results: list[GroupCreationResult]
    overall_success: bool

    @property
    def successful_groups(self) -> list[GroupCreationResult]:
        """Get list of successfully created groups.

        Returns:
            list[GroupCreationResult]: Successfully created groups
        """
        return [r for r in self.results if r.success]

    @property
    def failed_groups(self) -> list[GroupCreationResult]:
        """Get list of failed group creation attempts.

        Returns:
            list[GroupCreationResult]: Failed group creation attempts
        """
        return [r for r in self.results if not r.success]
