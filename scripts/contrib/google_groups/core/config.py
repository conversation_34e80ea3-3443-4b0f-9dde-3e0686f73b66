"""Configuration management for Google Groups application.

This module centralizes all configuration constants and settings.
"""

import os
from typing import Any

# Domain configuration
DOMAIN = "payitgov.com"

# OAuth 2.0 configuration
SCOPES = [
    "https://www.googleapis.com/auth/admin.directory.group",
    "https://www.googleapis.com/auth/admin.directory.group.member",
    "https://www.googleapis.com/auth/apps.groups.settings",
    "https://www.googleapis.com/auth/userinfo.email",
    "openid",
]

# Google Groups default settings
# These settings are applied to all created groups
# - "isArchived": Controls message archiving behavior (true = keep message history, false = no archive)
# - "archiveOnly": Controls group active/inactive status (false = active, true = inactive/read-only)
# For active groups with message history: isArchived=true, archiveOnly=false
GROUP_SETTINGS = {
    "allowExternalMembers": "true",
    "isArchived": "true",
    "showInGroupDirectory": "true",
    "whoCanContactOwner": "ALL_IN_DOMAIN_CAN_CONTACT",
    "whoCanJoin": "ALL_IN_DOMAIN_CAN_JOIN",
    "whoCanPostMessage": "ALL_IN_DOMAIN_CAN_POST",
    "whoCanViewGroup": "ALL_IN_DOMAIN_CAN_VIEW",
    "whoCanViewMembership": "ALL_IN_DOMAIN_CAN_VIEW",
}

# OAuth client configuration
OAUTH_CLIENT_CONFIG = {
    "installed": {
        "redirect_uris": ["http://localhost", "urn:ietf:wg:oauth:2.0:oob"],
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://accounts.google.com/o/oauth2/token",
    }
}

# API configuration
MAX_RETRIES = 3
BATCH_SIZE = 20
GROUP_PROPAGATION_DELAY_SECONDS = 5

# Group existence checking configuration
NAME_CONFLICT_PAGE_SIZE = 100  # Page size for name conflict checking (larger = fewer API calls)

# UI configuration
WINDOW_MIN_WIDTH = 600
LOG_AREA_MIN_HEIGHT = 50
LOG_AREA_MAX_HEIGHT = 100


def get_oauth_client_config() -> dict[str, Any]:
    """Get OAuth client configuration with credentials from environment.

    Returns:
        dict[str, Any]: OAuth client configuration

    Raises:
        ValueError: If required environment variables are not set or invalid
    """
    client_id = os.environ.get("GOOGLE_CLIENT_ID")
    client_secret = os.environ.get("GOOGLE_CLIENT_SECRET")

    if not client_id or not client_secret:
        raise ValueError("Required environment variables GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET must be set")

    # Validate client ID format (should be a long string ending in .apps.googleusercontent.com)
    if not client_id.endswith(".apps.googleusercontent.com") or len(client_id) < 50:
        raise ValueError("GOOGLE_CLIENT_ID appears to be invalid format")

    # Validate client secret format (should be a reasonable length string)
    if len(client_secret) < 20:
        raise ValueError("GOOGLE_CLIENT_SECRET appears to be too short to be valid")

    config = OAUTH_CLIENT_CONFIG.copy()
    config["installed"]["client_id"] = client_id
    config["installed"]["client_secret"] = client_secret

    return config


def validate_environment() -> None:
    """Validate that all required environment variables are set.

    Raises:
        ValueError: If required environment variables are missing or invalid
    """
    required_vars = ["GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET"]
    missing_vars = [var for var in required_vars if not os.environ.get(var)]

    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

    # Also validate the format by attempting to get the config
    try:
        get_oauth_client_config()
    except ValueError as e:
        raise ValueError(f"Environment validation failed: {e}") from e
