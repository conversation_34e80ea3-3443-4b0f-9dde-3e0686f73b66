"""Business logic orchestration for Google Groups management.

This module contains the core business logic for creating and configuring Google Groups,
separated from UI and API implementation details.
"""

import logging
import time
from typing import Callable

from scripts.contrib.google_groups.api.auth import AuthenticationError, GoogleAuthenticator
from scripts.contrib.google_groups.api.google_client import (
    GoogleAPIError,
    GoogleGroupsClient,
    GroupAlreadyExistsError,
    GroupNameConflictError,
)
from scripts.contrib.google_groups.core.config import GROUP_PROPAGATION_DELAY_SECONDS
from scripts.contrib.google_groups.core.models import (
    BatchCreationResult,
    GroupConfig,
    GroupCreationRequest,
    GroupCreationResult,
    GroupMembers,
)
from scripts.contrib.google_groups.ui.validators import (
    ValidationError,
    parse_and_validate_emails,
    validate_description,
    validate_email_prefix,
    validate_environment_selection,
    validate_group_name,
)

logger = logging.getLogger(__name__)


class GroupManagerError(Exception):
    """Raised when group management operations fail."""

    pass


class GoogleGroupManager:
    """Orchestrates Google Group creation and configuration operations."""

    def __init__(self, progress_callback: Callable[[str], None] | None = None) -> None:
        """Initialize the group manager.

        Args:
            progress_callback (Callable | None): Optional callback for progress updates
        """
        self.progress_callback = progress_callback
        self.authenticator = GoogleAuthenticator()
        self.api_client: GoogleGroupsClient | None = None

    def _log_progress(self, message: str) -> None:
        """Log progress message to callback and logger.

        Args:
            message (str): Progress message
        """
        logger.info(message)
        if self.progress_callback:
            self.progress_callback(message)

    def _initialize_api_client(self) -> None:
        """Initialize the Google API client.

        Raises:
            GroupManagerError: If API client initialization fails
        """
        if self.api_client is not None:
            return

        try:
            self._log_progress("Initializing Google API client...")
            self.api_client = GoogleGroupsClient(self.authenticator)
            self._log_progress("Google API client initialized successfully")
        except (AuthenticationError, GoogleAPIError) as e:
            raise GroupManagerError(f"Failed to initialize Google API client: {e}") from e

    def validate_request(self, request: GroupCreationRequest) -> None:
        """Validate a group creation request.

        Args:
            request (GroupCreationRequest): The request to validate

        Raises:
            GroupManagerError: If validation fails
        """
        try:
            # Check that at least one environment is specified
            if not request.prod_config and not request.staging_config:
                raise ValidationError("At least one environment configuration is required")

            # Validate each configuration
            if request.prod_config:
                validate_group_name(request.prod_config.name)
                validate_email_prefix(request.prod_config.email_prefix)
                validate_description(request.prod_config.description)

                # Validate environment-specific rules
                validate_environment_selection(
                    True, bool(request.staging_config), request.prod_config.name, is_staging_config=False
                )

            if request.staging_config:
                validate_group_name(request.staging_config.name)
                validate_email_prefix(request.staging_config.email_prefix)
                validate_description(request.staging_config.description)

                # Validate environment-specific rules
                validate_environment_selection(
                    bool(request.prod_config), True, request.staging_config.name, is_staging_config=True
                )

            # Validate member configurations
            if request.prod_members:
                self._validate_group_members(request.prod_members)

            if request.staging_members:
                self._validate_group_members(request.staging_members)

        except ValidationError as e:
            raise GroupManagerError(f"Request validation failed: {e}") from e

    def _validate_group_members(self, members: GroupMembers) -> None:
        """Validate group member configuration.

        Args:
            members (GroupMembers): Member configuration to validate

        Raises:
            ValidationError: If member configuration is invalid
        """
        # All member lists are optional, but if provided, emails must be valid
        for email in members.members:
            if not email.strip():
                raise ValidationError("Empty email address in members list")

        for email in members.managers:
            if not email.strip():
                raise ValidationError("Empty email address in managers list")

        for email in members.owners:
            if not email.strip():
                raise ValidationError("Empty email address in owners list")

    def create_groups(self, request: GroupCreationRequest) -> BatchCreationResult:
        """Create and configure Google Groups based on the request.

        Args:
            request (GroupCreationRequest): Group creation request

        Returns:
            BatchCreationResult: Results of the group creation operations

        Raises:
            GroupManagerError: If the operation fails completely
        """
        # Validate the request first
        self.validate_request(request)

        # Initialize API client
        self._initialize_api_client()

        if not self.api_client:
            raise GroupManagerError("API client not initialized")

        # Get current user's email to ensure they're added as owner
        try:
            current_user_email = self.api_client.get_current_user_email()
            self._log_progress(f"Current user: {current_user_email}")
        except GoogleAPIError as e:
            self._log_progress(f"Warning: Could not retrieve current user email: {e}")
            current_user_email = None

        # Ensure current user is in owners list for both environments
        if current_user_email:
            if request.prod_members:
                self._ensure_user_in_owners(request.prod_members, current_user_email)
            if request.staging_members:
                self._ensure_user_in_owners(request.staging_members, current_user_email)

        # Check if both environments are selected for batch optimization
        both_environments = (
            request.prod_config and request.prod_members and request.staging_config and request.staging_members
        )

        if both_environments:
            self._log_progress("Both environments selected")
            return self._create_groups_batch(request)
        else:
            # Single environment - use existing workflow
            return self._create_groups_sequential(request)

    def _create_groups_sequential(self, request: GroupCreationRequest) -> BatchCreationResult:
        """Create groups sequentially (original workflow for single environment).

        Args:
            request (GroupCreationRequest): Group creation request

        Returns:
            BatchCreationResult: Results of the group creation operations
        """
        results: list[GroupCreationResult] = []

        # Process each environment
        if request.prod_config and request.prod_members:
            self._log_progress(f"Processing production group: {request.prod_config.name}")
            prod_result = self._create_single_group(request.prod_config, request.prod_members, should_delay=True)
            results.append(prod_result)

        if request.staging_config and request.staging_members:
            self._log_progress(f"Processing staging group: {request.staging_config.name}")
            # Staging should also get delay to ensure proper API propagation
            staging_result = self._create_single_group(
                request.staging_config, request.staging_members, should_delay=True
            )
            results.append(staging_result)

        overall_success = all(result.success for result in results)

        return BatchCreationResult(results=results, overall_success=overall_success)

    def _create_groups_batch(self, request: GroupCreationRequest) -> BatchCreationResult:
        """Create groups using optimized batch workflow (both environments).

        This method creates both groups first, waits once (5 sec), then configures both.

        Args:
            request (GroupCreationRequest): Group creation request

        Returns:
            BatchCreationResult: Results of the group creation operations
        """
        results: list[GroupCreationResult] = []
        created_groups: list[tuple[GroupConfig, GroupMembers, str]] = []  # (config, members, created_email)

        # Step 1: Create both groups (without delays)
        self._log_progress("Step 1: Creating both group records...")

        # Type assertions - these are guaranteed to be non-None since we checked in create_groups
        assert request.prod_config is not None
        assert request.prod_members is not None
        assert request.staging_config is not None
        assert request.staging_members is not None

        # Create production group
        prod_result = self._create_group_only(request.prod_config)
        if prod_result.success:
            created_groups.append((request.prod_config, request.prod_members, prod_result.group_email))
        results.append(prod_result)

        # Create staging group
        staging_result = self._create_group_only(request.staging_config)
        if staging_result.success:
            created_groups.append((request.staging_config, request.staging_members, staging_result.group_email))
        results.append(staging_result)

        # Step 2: Single delay for API propagation
        if created_groups:
            self._log_progress(f"Step 2: Waiting {GROUP_PROPAGATION_DELAY_SECONDS} seconds for group propagation...")
            time.sleep(GROUP_PROPAGATION_DELAY_SECONDS)

            # Step 3: Configure all successfully created groups
            self._log_progress("Step 3: Configuring created groups...")
            for i, (config, members, created_email) in enumerate(created_groups):
                # Update the corresponding result with configuration status
                config_result = self._configure_group_only(config, members, created_email)

                # Find the matching result and update it
                for result in results:
                    if result.environment == config.environment:
                        if config_result.success:
                            # Keep the success status and update any additional errors
                            result.errors.extend(config_result.errors)
                        else:
                            # Configuration failed - mark as failed and add errors
                            result.success = False
                            result.errors.extend(config_result.errors)
                        break

        overall_success = all(result.success for result in results)

        return BatchCreationResult(results=results, overall_success=overall_success)

    def _create_group_only(self, config: GroupConfig) -> GroupCreationResult:
        """Create a group record only (no configuration or delay).

        Args:
            config (GroupConfig): Group configuration

        Returns:
            GroupCreationResult: Result of the group creation
        """
        if not self.api_client:
            return GroupCreationResult(
                success=False,
                group_email=config.full_email,
                group_url=config.group_url,
                errors=["API client not initialized"],
                environment=config.environment,
            )

        try:
            self._log_progress(f"Creating group record: {config.full_email}")
            created_email = self.api_client.create_group(config)
            self._log_progress(f"Successfully created group record: {created_email}")

            return GroupCreationResult(
                success=True,
                group_email=created_email,
                group_url=config.group_url,
                errors=[],
                environment=config.environment,
            )

        except GroupNameConflictError as e:
            error_msg = str(e)
            self._log_progress(f"Group name conflict detected: {error_msg}")
            return GroupCreationResult(
                success=False,
                group_email=config.full_email,
                group_url=config.group_url,
                errors=[error_msg],
                environment=config.environment,
            )

        except GroupAlreadyExistsError as e:
            error_msg = str(e)
            self._log_progress(f"Group already exists: {config.full_email}")
            return GroupCreationResult(
                success=False,
                group_email=config.full_email,
                group_url=config.group_url,
                errors=[error_msg],
                environment=config.environment,
            )

        except GoogleAPIError as e:
            error_msg = str(e)
            self._log_progress(f"Failed to create group {config.full_email}: {error_msg}")
            return GroupCreationResult(
                success=False,
                group_email=config.full_email,
                group_url=config.group_url,
                errors=[error_msg],
                environment=config.environment,
            )

    def _configure_group_only(
        self, config: GroupConfig, members: GroupMembers, group_email: str
    ) -> GroupCreationResult:
        """Configure a group (apply settings and add members) without creation or delay.

        Args:
            config (GroupConfig): Group configuration
            members (GroupMembers): Member configuration
            group_email (str): Email address of the created group

        Returns:
            GroupCreationResult: Result of the group configuration
        """
        if not self.api_client:
            return GroupCreationResult(
                success=False,
                group_email=group_email,
                group_url=config.group_url,
                errors=["API client not initialized"],
                environment=config.environment,
            )

        errors: list[str] = []

        try:
            # Apply settings
            self._log_progress(f"Applying settings to: {group_email}")
            self.api_client.apply_group_settings(group_email)

            # Add members
            self._log_progress(f"Adding members to: {group_email}")
            self.api_client.add_members_to_group(group_email, members)

            self._log_progress(f"Successfully configured group: {group_email}")

            return GroupCreationResult(
                success=True,
                group_email=group_email,
                group_url=config.group_url,
                errors=errors,
                environment=config.environment,
            )

        except GoogleAPIError as e:
            error_msg = str(e)
            errors.append(error_msg)
            self._log_progress(f"Failed to configure group {group_email}: {error_msg}")

            return GroupCreationResult(
                success=False,
                group_email=group_email,
                group_url=config.group_url,
                errors=errors,
                environment=config.environment,
            )

    def _ensure_user_in_owners(self, members: GroupMembers, user_email: str) -> None:
        """Ensure the specified user is in the owners list.

        Args:
            members (GroupMembers): Member configuration to modify
            user_email (str): Email address to ensure is in owners list
        """
        if user_email not in members.owners:
            members.owners.append(user_email)
            self._log_progress(f"Added current user {user_email} to group owners")

    def _create_single_group(
        self, config: GroupConfig, members: GroupMembers, should_delay: bool = True
    ) -> GroupCreationResult:
        """Create and configure a single Google Group.

        Args:
            config (GroupConfig): Group configuration
            members (GroupMembers): Member configuration
            should_delay (bool): Whether to apply propagation delay before settings/members

        Returns:
            GroupCreationResult: Result of the group creation
        """
        if not self.api_client:
            return GroupCreationResult(
                success=False,
                group_email=config.full_email,
                group_url=config.group_url,
                errors=["API client not initialized"],
                environment=config.environment,
            )

        errors: list[str] = []

        try:
            # Step 1: Create the group
            self._log_progress(f"Creating group record: {config.full_email}")
            created_email = self.api_client.create_group(config)

            # Step 2: Wait for propagation to avoid API errors
            if should_delay:
                self._log_progress(f"Waiting {GROUP_PROPAGATION_DELAY_SECONDS} seconds for group propagation...")
                time.sleep(GROUP_PROPAGATION_DELAY_SECONDS)

            # Step 3: Apply settings
            self._log_progress(f"Applying settings to: {created_email}")
            self.api_client.apply_group_settings(created_email)

            # Step 4: Add members
            self._log_progress(f"Adding members to: {created_email}")
            self.api_client.add_members_to_group(created_email, members)

            self._log_progress(f"Successfully created and configured group: {created_email}")

            return GroupCreationResult(
                success=True,
                group_email=created_email,
                group_url=config.group_url,
                errors=errors,
                environment=config.environment,
            )

        except GroupNameConflictError as e:
            error_msg = str(e)
            errors.append(error_msg)
            self._log_progress(f"Group name conflict detected: {error_msg}")

            return GroupCreationResult(
                success=False,
                group_email=config.full_email,
                group_url=config.group_url,
                errors=errors,
                environment=config.environment,
            )

        except GroupAlreadyExistsError as e:
            error_msg = str(e)
            errors.append(error_msg)
            self._log_progress(f"Group already exists: {config.full_email}")

            return GroupCreationResult(
                success=False,
                group_email=config.full_email,
                group_url=config.group_url,
                errors=errors,
                environment=config.environment,
            )

        except GoogleAPIError as e:
            error_msg = str(e)
            errors.append(error_msg)
            self._log_progress(f"Failed to create/configure group {config.full_email}: {error_msg}")

            return GroupCreationResult(
                success=False,
                group_email=config.full_email,
                group_url=config.group_url,
                errors=errors,
                environment=config.environment,
            )


def create_group_request_from_form_data(
    name: str,
    email_prefix: str,
    description: str,
    prod_selected: bool,
    staging_selected: bool,
    members_text: str,
    managers_text: str,
    owners_text: str,
    prod_members_text: str = "",
    staging_members_text: str = "",
    prod_managers_text: str = "",
    staging_managers_text: str = "",
    prod_owners_text: str = "",
    staging_owners_text: str = "",
    prod_descriptions_text: str = "",
    staging_descriptions_text: str = "",
    same_members: bool = True,
    same_managers: bool = True,
    same_owners: bool = True,
    same_descriptions: bool = True,
) -> GroupCreationRequest:
    """Create a GroupCreationRequest from form data.

    Args:
        name (str): Group name
        email_prefix (str): Email prefix
        description (str): Group description
        prod_selected (bool): Whether production is selected
        staging_selected (bool): Whether staging is selected
        members_text (str): Shared members text
        managers_text (str): Shared managers text
        owners_text (str): Shared owners text
        prod_members_text (str): Production-specific members text
        staging_members_text (str): Staging-specific members text
        prod_managers_text (str): Production-specific managers text
        staging_managers_text (str): Staging-specific managers text
        prod_owners_text (str): Production-specific owners text
        staging_owners_text (str): Staging-specific owners text
        prod_descriptions_text (str): Production-specific descriptions text
        staging_descriptions_text (str): Staging-specific descriptions text
        same_members (bool): Whether to use same members for both environments
        same_managers (bool): Whether to use same managers for both environments
        same_owners (bool): Whether to use same owners for both environments
        same_descriptions (bool): Whether to use same descriptions for both environments

    Returns:
        GroupCreationRequest: Validated group creation request

    Raises:
        GroupManagerError: If form data validation fails
    """
    try:
        # Validate and normalize inputs
        validated_name = validate_group_name(name)
        validated_prefix = validate_email_prefix(email_prefix)
        validated_description = validate_description(description)

        # Parse member lists
        shared_members = parse_and_validate_emails(members_text)
        shared_managers = parse_and_validate_emails(managers_text)
        shared_owners = parse_and_validate_emails(owners_text)

        # Determine member configurations for each environment
        prod_config = None
        staging_config = None
        prod_members = None
        staging_members = None

        if prod_selected:
            # Determine description for prod
            if same_descriptions:
                prod_description = validated_description
            else:
                prod_description = validate_description(prod_descriptions_text)

            prod_config = GroupConfig(
                name=validated_name,
                email_prefix=validated_prefix,
                description=prod_description,
                environment="prod",
            )

            # Determine member lists for prod
            if same_members:
                prod_member_emails = shared_members
            else:
                prod_member_emails = parse_and_validate_emails(prod_members_text)

            if same_managers:
                prod_manager_emails = shared_managers
            else:
                prod_manager_emails = parse_and_validate_emails(prod_managers_text)

            if same_owners:
                prod_owner_emails = shared_owners
            else:
                prod_owner_emails = parse_and_validate_emails(prod_owners_text)

            prod_members = GroupMembers(
                members=prod_member_emails, managers=prod_manager_emails, owners=prod_owner_emails
            )

        if staging_selected:
            # Generate staging names
            staging_name = validated_name if "staging" in validated_name.lower() else f"{validated_name} Staging"
            staging_prefix = (
                validated_prefix if "staging" in validated_prefix.lower() else f"{validated_prefix}-staging"
            )

            # Determine description for staging
            if same_descriptions:
                staging_description = validated_description
            else:
                staging_description = validate_description(staging_descriptions_text)

            staging_config = GroupConfig(
                name=staging_name, email_prefix=staging_prefix, description=staging_description, environment="staging"
            )

            # Determine member lists for staging
            if same_members:
                staging_member_emails = shared_members
            else:
                staging_member_emails = parse_and_validate_emails(staging_members_text)

            if same_managers:
                staging_manager_emails = shared_managers
            else:
                staging_manager_emails = parse_and_validate_emails(staging_managers_text)

            if same_owners:
                staging_owner_emails = shared_owners
            else:
                staging_owner_emails = parse_and_validate_emails(staging_owners_text)

            staging_members = GroupMembers(
                members=staging_member_emails, managers=staging_manager_emails, owners=staging_owner_emails
            )

        return GroupCreationRequest(
            prod_config=prod_config,
            staging_config=staging_config,
            prod_members=prod_members,
            staging_members=staging_members,
        )

    except ValidationError as e:
        raise GroupManagerError(f"Form validation failed: {e}") from e
