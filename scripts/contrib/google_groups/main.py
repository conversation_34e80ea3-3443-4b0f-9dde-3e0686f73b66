#!/usr/bin/env python3
"""Main entry point for Google Groups Creator application.

This script initializes and runs the Google Groups Creator GUI application.
"""

import logging
import sys

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
except ImportError:
    print("ERROR: PySide6 is required. Install with: pip install PySide6")
    sys.exit(1)

from scripts.contrib.google_groups.core.config import validate_environment
from scripts.contrib.google_groups.ui.main_window import GoogleGroupCreatorWindow

# Load Environment variables by go-task via .env-google

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger("google_group_creator")


def main() -> None:
    """Main entry point for the application."""
    try:
        # Validate environment variables
        validate_environment()

        # Create QApplication
        app = QApplication(sys.argv)

        # Create and show main window
        window = GoogleGroupCreatorWindow()
        window.show()

        # Run the application
        sys.exit(app.exec())

    except ValueError as e:
        # Environment validation failed
        error_msg = f"Configuration error: {e}"
        logger.error(error_msg)

        # Show error dialog if QApplication can be created
        try:
            app = QApplication(sys.argv)
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setWindowTitle("Configuration Error")
            msg_box.setText(error_msg)
            msg_box.exec()
        except Exception:
            # If GUI fails, just print to console
            print(f"ERROR: {error_msg}")

        sys.exit(1)

    except Exception as e:
        # Unexpected error
        error_msg = f"Failed to start application: {e}"
        logger.exception(error_msg)

        try:
            app = QApplication(sys.argv)
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setWindowTitle("Application Error")
            msg_box.setText(error_msg)
            msg_box.exec()
        except Exception:
            print(f"ERROR: {error_msg}")

        sys.exit(1)


if __name__ == "__main__":
    main()
